import { Alert, Linking, Platform } from 'react-native';
import * as ImagePicker from 'expo-image-picker';

async function promptToOpenSettings(title: string, message: string): Promise<void> {
	return new Promise((resolve) => {
		Alert.alert(title, message, [
			{ text: 'Cancel', style: 'cancel', onPress: () => resolve() },
			{
				text: 'Open Settings',
				onPress: async () => {
					try {
						await Linking.openSettings();
					} catch {}
					resolve();
				},
			},
		]);
	});
}

export async function ensureMediaLibraryPermission(): Promise<boolean> {
	const { status: existingStatus, canAskAgain } = await ImagePicker.getMediaLibraryPermissionsAsync();
	if (existingStatus === 'granted') return true;

	if (canAskAgain) {
		const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
		if (status === 'granted') return true;
	}

	await promptToOpenSettings(
		'Permission Required',
		Platform.select({
			ios: 'Please enable Photos access in Settings to select images.',
			android: 'Please enable Media/Photos access in Settings to select images.',
			default: 'Please enable media library access in Settings to select images.',
		}) || 'Please enable media library access in Settings to select images.'
	);
	return false;
}

export async function ensureCameraPermission(): Promise<boolean> {
	const { status: existingStatus, canAskAgain } = await ImagePicker.getCameraPermissionsAsync();
	if (existingStatus === 'granted') return true;

	if (canAskAgain) {
		const { status } = await ImagePicker.requestCameraPermissionsAsync();
		if (status === 'granted') return true;
	}

	await promptToOpenSettings(
		'Permission Required',
		Platform.select({
			ios: 'Please enable Camera access in Settings to take photos.',
			android: 'Please enable Camera access in Settings to take photos.',
			default: 'Please enable camera access in Settings to take photos.',
		}) || 'Please enable camera access in Settings to take photos.'
	);
	return false;
}
