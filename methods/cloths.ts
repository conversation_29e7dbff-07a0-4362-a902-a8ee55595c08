import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import Meteor from '@meteorrn/core';

// Cache for category mappings to avoid repeated API calls
let categoryMappingCache: { [key: string]: string } | null = null;

// Function to build category mapping from backend categories
async function buildCategoryMapping(): Promise<{ [key: string]: string }> {
  if (categoryMappingCache) {
    return categoryMappingCache;
  }

  try {
    // Fetch categories from backend
    const categoriesResult = await new Promise<any>((resolve, reject) => {
      Meteor.call('itemCategories-fetch', { gender: 'men' }, (err: any, res: any) => {
        if (err) {
          console.error('Error fetching categories for mapping:', err);
          reject(err);
          return;
        }
        resolve(res);
      });
    });

    const mapping: { [key: string]: string } = {};

    if (categoriesResult?.success && categoriesResult.data?.itemCategories) {
      console.log('🔍 Building category mapping from', categoriesResult.data.itemCategories.length, 'categories');

      categoriesResult.data.itemCategories.forEach((category: any, index: number) => {
        console.log(`Category ${index + 1}:`, {
          _id: category._id,
          mainCategory: category.mainCategory,
          subCategoriesCount: category.subCategories?.length || 0
        });

        if (category._id && category.mainCategory) {
          // Map main category ID to name
          mapping[category._id] = category.mainCategory;
          console.log(`  ✅ Mapped main category: ${category._id} -> ${category.mainCategory}`);
        }

        // Map subcategories if they exist
        if (category.subCategories && Array.isArray(category.subCategories)) {
          category.subCategories.forEach((subCat: any) => {
            if (subCat._id && subCat.name) {
              mapping[subCat._id] = subCat.name;
              console.log(`  ✅ Mapped subcategory: ${subCat._id} -> ${subCat.name}`);
            }
          });
        }
      });
    }

    // Add some common fallback mappings
    mapping['basic-shoes'] = 'Shoes';
    mapping['basic-tops'] = 'Tops';
    mapping['basic-bottoms'] = 'Bottoms';
    mapping['basic-tech'] = 'Tech';

    categoryMappingCache = mapping;
    console.log('Built category mapping with', Object.keys(mapping).length, 'entries');
    return mapping;
  } catch (error) {
    console.error('Error building category mapping:', error);
    // Return basic fallback mapping
    return {
      'basic-shoes': 'Shoes',
      'basic-tops': 'Tops',
      'basic-bottoms': 'Bottoms',
      'basic-tech': 'Tech'
    };
  }
}

type CategoriesResponse = {
  success: boolean;
  data: any;
};

type ImageUploadResponse = {
  success: boolean;
  data: any;
  [key: string]: any;
};

export const getCategories = () =>
  useQuery({
    queryKey: ['categories'],
    queryFn: () => {
      return new Promise<CategoriesResponse>((resolve, reject) => {
        Meteor.call(
          'itemCategories-fetch',
          (err: any, res: CategoriesResponse) => {
            if (err) {
              console.log(err);
              reject(err);
              return;
            }

            console.log(res);

            // Ensure we never resolve with undefined
            if (!res || !res.data) {
              resolve({
                success: true,
                data: { itemCategories: [] }
              });
              return;
            }

            resolve(res.data);
          },
        );
      });
    },
  });

// Helper function to process clothes data consistently
async function processClothesData(data: any) {
  if (!data) {
    return { items: [] };
  }

  if (!data.items) {
    return { items: [] };
  }

  if (!Array.isArray(data.items)) {
    return { items: [] };
  }

  // Filter out invalid items
  const filteredItems = data.items.filter((item: any) => {
    if (!item) {
      return false;
    }

    // Keep all valid items (including test items for development)
    return true;
  });

  // Get category mapping
  const categoryMapping = await buildCategoryMapping();

  // Process image URLs and add category names
  const processedItems = filteredItems.map((item: any) => {
    // Ensure imageUrl is properly formatted
    if (item.imageUrl && !item.imageUrl.startsWith('http')) {
      item.imageUrl = `https://myuse.s3.ap-southeast-1.amazonaws.com/${item.imageUrl}`;
    }

    // Add categoryName based on itemCategoryId
    if (item.itemCategoryId && categoryMapping[item.itemCategoryId]) {
      item.categoryName = categoryMapping[item.itemCategoryId];
      console.log(`✅ Mapped ${item.itemCategoryId} -> ${item.categoryName}`);
    } else if (item.itemCategoryId) {
      // Fallback: try to extract category from ID
      const fallbackName = getFallbackCategoryName(item.itemCategoryId);
      item.categoryName = fallbackName;
      console.log(`⚠️ No mapping found for ${item.itemCategoryId}, using fallback: ${fallbackName}`);
    }

    return item;
  });

  return { ...data, items: processedItems };
}

// Fallback function to extract category name from itemCategoryId
function getFallbackCategoryName(itemCategoryId: string): string {
  const id = itemCategoryId.toLowerCase();

  // Check for common patterns in category IDs
  if (id.includes('shoe') || id.includes('basic-shoes')) return 'Shoes';
  if (id.includes('top') || id.includes('shirt') || id.includes('tshirt')) return 'Tops';
  if (id.includes('bottom') || id.includes('pant') || id.includes('jean')) return 'Bottoms';
  if (id.includes('tech') || id.includes('electronic')) return 'Tech';
  if (id.includes('dress')) return 'Dresses';
  if (id.includes('access')) return 'Accessories';
  if (id.includes('bag')) return 'Bags';
  if (id.includes('jewelry') || id.includes('jewellery')) return 'Jewelry';

  // For unmapped legacy IDs, provide generic category names based on common patterns
  // These are likely old category IDs that don't exist in the current backend
  const legacyMappings: { [key: string]: string } = {
    'hMXnSJEru8uph8qL5': 'Tops',
    'TXnwWN3k83q3JE3Lm': 'Bottoms',
    'ZnPdEhJKbF78onLRd': 'Tech',
    '494Zk9ujgfKX4gnwD': 'Accessories',
    'ffciNZSc5baMLtsbA': 'Others'
  };

  // Check if this is a known legacy ID
  if (legacyMappings[itemCategoryId]) {
    return legacyMappings[itemCategoryId];
  }

  // If no match, return "Others" for unknown categories
  return 'Others';
}

// Interface for getClothes parameters
interface GetClothesParams {
  generalCategory?: string;
  mainCategory?: string; // For dual-level filtering: main category like "CLOTHES", "ACCESSORIES", "TECH"
  subCategory?: string;  // For dual-level filtering: subcategory like "Tops", "Bottoms", "Shoes"
  skip?: number;
  limit?: number;
}

// Main category mapping for dual-level system
export const MAIN_CATEGORIES = {
  CLOTHES: 'CLOTHES',
  ACCESSORIES: 'ACCESSORIES',
  TECH: 'TECH',
  TOILETRIES: 'TOILETRIES',
  OTHER: 'OTHER'
} as const;

// Sub-category mapping for each main category
export const SUB_CATEGORIES = {
  'CLOTHES': ['Tops', 'Bottoms'],
  'Clothes': ['Tops', 'Bottoms'],
  'ACCESSORIES': ['Shoes'],
  'Accessories': ['Shoes'],
  'TECH': ['Tech'],
  'Tech/Electronic': ['Tech'],
  'TOILETRIES': [],
  'Toiletries': [],
  'OTHER': ['Others'],
  'Other': ['Others']
} as const;

// Helper function to get main category from sub-category
export const getMainCategoryFromSub = (subCategory: string): string | undefined => {
  for (const [mainCat, subCats] of Object.entries(SUB_CATEGORIES)) {
    if (subCats.includes(subCategory as any)) {
      return mainCat;
    }
  }
  return undefined;
};

export const getClothes = (params: GetClothesParams = {}) =>
  useQuery({
    queryKey: [
      'clothes',
      params.generalCategory || 'all',
      params.mainCategory || 'all',
      params.subCategory || 'all',
      params.skip || 0,
      params.limit || 50
    ],
    queryFn: async () => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        console.log('🔄 Using items-fetchAll with params:', params);

        // Temporarily use items-fetchAll until items-fetchAll2 is available on backend
        const methodName = 'items-fetchAll';
        const methodParams = {
          generalCategory: params.generalCategory,
          skip: params.skip,
          limit: params.limit
        };

        console.log(`🔄 Using ${methodName} with params:`, methodParams);

        Meteor.call(
          methodName,
          methodParams,
          async (err: any, res: ImageUploadResponse) => {
            // Helper function to process the response
            const processResponse = async (response: ImageUploadResponse, methodUsed: string) => {
              if (!response.data) {
                resolve({
                  success: true,
                  data: { items: [] }
                });
                return;
              }

              if (!response.data.items || !Array.isArray(response.data.items)) {
                resolve({
                  success: true,
                  data: { items: [] }
                });
                return;
              }

              console.log(`✅ ${methodUsed} returned ${response.data.items.length} items`);

              try {
                // Process data consistently here (now async)
                const processedData = await processClothesData(response.data);
                resolve(processedData);
              } catch (error) {
                console.error('Error processing clothes data:', error);
                // Fallback to unprocessed data
                resolve(response.data);
              }
            };

            if (err) {
              console.error(`Error in ${methodName}:`, err);
              reject(err);
              return;
            }

            // Process successful response
            await processResponse(res, methodName);
          },
        );
      });
    },
    // Increase staleTime to reduce unnecessary refetches
    staleTime: 5 * 60 * 1000, // 5 minutes
    // Remove automatic refetchInterval to prevent excessive API calls
    // refetchInterval: 60 * 1000, // 1 minute
    // Only refetch when the component mounts
    refetchOnMount: true,
    // Don't refetch on window focus to prevent excessive API calls
    refetchOnWindowFocus: false
  });

export const addItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (item: any) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call('items-add', item, (err: any, res: ImageUploadResponse) => {
          if (err) {
            reject(err);
            return;
          }

          // Check if res.data exists and has the expected structure
          if (res && res.data) {
            resolve(res.data);
          } else {
            resolve(res);
          }
        });
      });
    },
    onSuccess: () => {
      // Invalidate the clothes cache to ensure the Closet screen updates
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Force a refetch of all clothes data after a short delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

export const ItemsUpdate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (item: any) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call(
          'items-update',
          item,
          (err: any, res: ImageUploadResponse) => {
            if (err) {
              reject(err);
              return;
            }

            resolve(res.data);
          },
        );
      });
    },
    onSuccess: () => {
      // Invalidate the clothes cache to ensure the Closet screen updates
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Force a refetch of all clothes data after a short delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

//   const handleUploadFile = async () => {
//     const file = document.getElementById('file-input').files[0];
//     const params = {
//             fileName: file?.name,
//             fileType: file?.type,
//             folderPath: 'avatars'
//     };
//     const result = await Meteor.callAsync("AWS-generatePreSignedUrl", params);
//     const {
//             signedURL,
//             fileURL // This is the file URL once upload is successful
//     } = result?.data;
//     fetch(signedURL, {
//             method: 'PUT',
//             body: file,
//             headers: {'Content-Type': file?.type}
//     })
//         .then(response => {
//                 if (response.ok) {
//                         console.log('File uploaded successfully', response);
//                         // Todo: Save fileURL in DB
//                 } else {
//                         console.error('Failed to upload file', response);
//                 }
//         })
//         .catch(error => {
//                 console.error('Error uploading file', error);
//         });
// };

export const UploadImage = () => {
  return useMutation({
    mutationFn: ({
      // imageUrl is unused but kept for type compatibility
      fileName,
      fileType,
      folderPath,
    }: {
      imageUrl: string;
      fileName: string;
      fileType: string;
      folderPath: string;
    }) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call(
          'AWS-generatePreSignedUrl',
          {
            fileName,
            fileType,
            folderPath,
          },
          (err: any, res: ImageUploadResponse) => {
            if (err) {
              reject(err);
              return;
            }

            resolve(res.data);
          },
        );
      });
    },
    onSuccess: () => {
      // Success handling is done by the component
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

const base64ToBlob = async (encoded: string) => {
  // check if base64 already has data:image/jpg;base64 prefix
  if (encoded.startsWith('data:image/jpg;base64,')) {
    let url = `${encoded}`;
    let res = await fetch(url);
    let blob = await res?.blob();
    return blob;
  }

  let url = `data:image/jpg;base64,${encoded}`;
  let res = await fetch(url);
  let blob = await res?.blob();
  return blob;
};

export const uploadImageToS3 = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({
      imageUrl,
      preSignedUrl,
    }: {
      imageUrl: string;
      preSignedUrl: string;
    }) => {
      return new Promise<ImageUploadResponse>(async (resolve, reject) => {
        try {
          const file = await base64ToBlob(imageUrl);

        fetch(preSignedUrl, {
          method: 'PUT',
          body: file,
          headers: {
            'Content-Type': 'image/jpeg',
          },
        }).then((response) => {
          if (response.ok) {
            // Create a proper response object that matches ImageUploadResponse
            resolve({
              success: true,
              data: { url: preSignedUrl }
            });
          } else {
            reject(response);
          }
        })
          .catch((error) => {
            reject(error);
          });
        } catch (error) {
          reject(error);
        }
      });
    },
    onSuccess: () => {
      // Invalidate the clothes cache to ensure the Closet screen updates
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Force a refetch of all clothes data after a short delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

// Delete an item by ID
export const deleteItem = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (itemId: string) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call('items-delete', { itemId }, (err: any, res: ImageUploadResponse) => {
          if (err) {
            reject(err);
            return;
          }

          resolve(res);
        });
      });
    },
    onSuccess: () => {
      // Invalidate the clothes cache to ensure the Closet screen updates
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Force a refetch of all clothes data after a short delay
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);
    },
    onError: () => {
      // Error handling is done by the component
    }
  });
};

//update item
export const updateItem = () =>
  useMutation({
    mutationFn: (item: any) => {
      return new Promise<ImageUploadResponse>((resolve, reject) => {
        Meteor.call('items-update', item, (err: any, res: ImageUploadResponse) => {
          if (err) {
            reject(err);
            return;
          }

          resolve(res.data);
        });
      });
    },
  });
