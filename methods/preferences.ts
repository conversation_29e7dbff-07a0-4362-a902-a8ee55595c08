import Meteor from '@meteorrn/core';
import { useMutation, useQuery } from '@tanstack/react-query';
import { useQueryClient } from '@tanstack/react-query';

// Common types
type BaseResponse = {
  success: boolean;
  data: any;
};

type StylePreferencesData = {
  stylePreferences: {
    [key: string]: any;
  };
};

type StylePreferencesResponse = BaseResponse & {
  data: StylePreferencesData;
};

type SizingChartsResponse = BaseResponse & {
  data: {
    sizingCharts: any[];
  };
};

// Generic Meteor call wrapper
const meteorCall = <T>(methodName: string, params: any = {}): Promise<T> => {
  return new Promise((resolve, reject) => {
    Meteor.call(methodName, params, (err: any, res: T) => {
      if (err) {
        console.log(err);
        reject(err);
        return;
      }
      resolve(res);
    });
  });
};

// Style Preferences
export const getStylePreferences = () =>
  useQuery({
    queryKey: ['stylePreferences'],
    queryFn: () => meteorCall<StylePreferencesResponse>('stylePreferences-fetch'),
    staleTime: 0, // Consider data stale immediately
    refetchOnMount: true,
    refetchOnWindowFocus: true,
  });

// Update functions
export const updateStyleHardPasses = () =>
  useMutation({
    mutationFn: ({ hardPasses }: { hardPasses: string[] }) =>
      meteorCall<BaseResponse>('stylePreferences-addHardPasses', { hardPasses }),
  });

export const updateStyleColors = () =>
  useMutation({
    mutationFn: ({ colors }: { colors: string[] }) =>
      meteorCall<BaseResponse>('stylePreferences-addColors', { colors }),
  });

export const updateStyleKeywords = () =>
  useMutation({
    mutationFn: ({ keywords }: { keywords: string[] }) =>
      meteorCall<BaseResponse>('stylePreferences-addKeywords', { keywords }),
  });

export const updateVegan = () =>
  useMutation({
    mutationFn: ({ isVegan }: { isVegan: boolean }) =>
      meteorCall<BaseResponse>('stylePreferences-setVegan', { isVegan }),
  });

// Sizing Charts
export const getSizingCharts = (gender?: 'men' | 'women') =>
  useQuery({
    queryKey: ['sizingCharts', gender || 'all'],
    queryFn: () => {
      const params = gender ? { gender } : {};
      return meteorCall<SizingChartsResponse>('sizingCharts-fetch', params);
    },
  });

export const updateSizingStandard = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ sizingStandard }: { sizingStandard: string }) =>
      meteorCall<BaseResponse>('stylePreferences-addSizingStandard', { sizingStandard }),
    onSuccess: () => {
      // Invalidate and refetch style preferences
      queryClient.invalidateQueries({ queryKey: ['stylePreferences'] });
    },
  });
};


// Factory function for size update mutations
const createSizeUpdateMutation = (methodName: string) => () =>
  useMutation({
    mutationFn: (params: { size: string; region?: string }) =>
      meteorCall<BaseResponse>(methodName, { region: params.region!, size: params.size }),
  });

// Men's sizing methods
export const updateMensTrousersSize = createSizeUpdateMutation('stylePreferences-addMensTrousersSize');
export const updateMensJeansSize = createSizeUpdateMutation('stylePreferences-addMensJeansSize');
export const updateMensOuterwearSize = createSizeUpdateMutation('stylePreferences-addMensOuterwearSize');
export const updateMensShoesSize = createSizeUpdateMutation('stylePreferences-addMensShoesSize');
export const updateMensTopsSize = createSizeUpdateMutation('stylePreferences-addMensTopsSize');

// Women's sizing methods
export const updateWomensDressesSize = createSizeUpdateMutation('stylePreferences-addWomensDressesSize');
export const updateWomensTopsSize = createSizeUpdateMutation('stylePreferences-addWomensTopsSize');
export const updateWomensTrousersSize = createSizeUpdateMutation('stylePreferences-addWomensTrousersSize');
export const updateWomensJeansSize = createSizeUpdateMutation('stylePreferences-addWomensJeansSize');
export const updateWomensFootwearSize = createSizeUpdateMutation('stylePreferences-addWomensFootwearSize');

type ColorPresetsResponse = {
  success: boolean;
  data: {
    colorPresets: any[];
  };
  message: string;
};

export const getColorPresets = () =>
  useQuery({
    queryKey: ['colorPresets'],
    queryFn: () => {
      return new Promise<ColorPresetsResponse>((resolve, reject) => {
        Meteor.call(
          'colorPresets-fetch',
          {},
          (err: any, res: ColorPresetsResponse) => {
            if (err) {
              reject(err);
              return;
            }
            resolve(res);
            return;
          },
        );
      });
    },
  });

type ColorGroupsResponse = {
  success: boolean;
  data: {
    colorGroups: any[];
  };
  message: string;
};

export const getColorGroups = () =>
  useQuery({
    queryKey: ['colorGroups'],
    queryFn: () => {
      return new Promise<ColorGroupsResponse>((resolve, reject) => {
        Meteor.call(
          'colorGroups-fetch',
          {},
          (err: any, res: ColorGroupsResponse) => {
            if (err) {
              reject(err);
              return;
            }
            resolve(res);
            return;
          },
        );
      });
    },
  });

export const updateColorGroup = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: ({ _id, preference }: { _id: string; preference: 'yes' | 'no' | 'maybe' }) =>
      meteorCall<BaseResponse>('stylePreferences-updateColorGroup', { _id, preference }),
    onSuccess: async () => {
      // Invalidate and refetch both queries to show updated preferences
      queryClient.invalidateQueries({ queryKey: ['stylePreferences'] });
      queryClient.invalidateQueries({ queryKey: ['colorGroups'] });
      // Force immediate refetch and wait for it
      await queryClient.refetchQueries({ queryKey: ['stylePreferences'] });
    },
    onError: (error) => {
      console.error('Error in updateColorGroup mutation:', error);
    },
  });
};

  