import { SettingsGroup } from '@/components/SettingsGroup';
import { SettingsSection } from '@/components/SettingsSection';
import HeaderPage from '@/components/common/HeaderPage';
import { MENS_SIZE_CHARTS, WOMENS_SIZE_CHARTS } from '@/constants/size-charts';
import {
  getStylePreferences,
  updateMensJeansSize,
  updateMensOuterwearSize,
  updateMensShoesSize,
  updateMensTopsSize,
  // Men's sizing methods
  updateMensTrousersSize,
  updateSizingStandard,
  // Women's sizing methods
  updateWomensDressesSize,
  updateWomensFootwearSize,
  updateWomensJeansSize,
  updateWomensTopsSize,
  updateWomensTrousersSize
} from '@/methods/preferences';
import { getUserProfile } from '@/methods/users';
import { useQueryClient } from '@tanstack/react-query';
import { useEffect, useMemo, useState } from 'react';
import { View } from 'react-native';

export default function SizingPreferences() {
  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();
  const { data: stylePreferences } = getStylePreferences();
  const queryClient = useQueryClient();
  const { mutate: setSizingStandard } = updateSizingStandard();

  // Size mutations configuration
  const sizeMutations = {
    men: {
      mensTops: updateMensTopsSize(),
      mensOuterwear: updateMensOuterwearSize(),
      mensTrousers: updateMensTrousersSize(),
      mensJeans: updateMensJeansSize(),
      mensShoes: updateMensShoesSize(),
    },
    women: {
      womensTops: updateWomensTopsSize(),
      womensDresses: updateWomensDressesSize(),
      womensTrousers: updateWomensTrousersSize(),
      womensJeans: updateWomensJeansSize(),
      womensFootwear: updateWomensFootwearSize(),
    },
  };

  const stylePreferencesData = stylePreferences?.data?.stylePreferences || {};

  // Determine user gender (convert to API format)
  const userGender = useMemo(() => {
    const profileGender = userProfile?.data?.profile?.gender;
    if (profileGender === 'Male') return 'men';
    if (profileGender === 'Female') return 'women';
    return 'women'; // Default fallback
  }, [userProfile]);

  // Get sizing charts based on user gender from static data
  const sizingChartsData = useMemo(() => {
    return userGender === 'men' ? MENS_SIZE_CHARTS : WOMENS_SIZE_CHARTS;
  }, [userGender]);

  const sizingStandardFromQuery = stylePreferences?.data?.stylePreferences?.sizingStandard;

  // State variable for sizing standard to ensure re-renders
  const [sizingStandard, setSizingStandardState] = useState<string | undefined>(sizingStandardFromQuery || 'US');

  // Update state when query data changes
  useEffect(() => {
    if (sizingStandardFromQuery !== sizingStandard) {
      setSizingStandardState(sizingStandardFromQuery);
    }
  }, [sizingStandardFromQuery, sizingStandard]);

  // Create dynamic sizing standard options from chart regions
  const dynamicSizingSetting = useMemo(() => {
    const charts = sizingChartsData || [];
    if (!charts.length) return [];

    // Get unique regions from all charts
    const allRegions = new Set<string>();
    charts.forEach((chart: any) => {
      chart.regions?.forEach((region: any) => {
        if (region.name) {
          allRegions.add(region.name);
        }
      });
    });

    // Convert to options array
    const regionOptions = Array.from(allRegions).map(region => ({
      label: region.toUpperCase(),
      value: region.toUpperCase(),
    }));

    return [{
      title: 'Sizing Standard',
      itemKey: 'sizingStandard',
      options: regionOptions,
      mutation: {
        mutate: (data: any) => {
          // Extract the value from the object passed by SettingsItem
          const sizingStandardValue = data.sizingStandard || 'US';

          // Get the old sizing standard to map sizes - capture it before the mutation
          // Get the current data from the query client to avoid stale closure issues
          const currentData = queryClient.getQueryData(['stylePreferences']) as any;
          const oldSizingStandard = currentData?.data?.stylePreferences?.sizingStandard;
          setSizingStandard({ sizingStandard: sizingStandardValue }, {
            onSuccess: (response) => {
              // Update the state variable immediately
              setSizingStandardState(sizingStandardValue);

              // Map existing size selections to the new sizing standard
              if (oldSizingStandard && oldSizingStandard !== sizingStandardValue && oldSizingStandard !== undefined) {
                const sizeFields = [
                  'mensTopsSize', 'mensOuterwearSize', 'mensTrousersSize', 'mensJeansSize', 'mensShoesSize',
                  'womensTopsSize', 'womensDressesSize', 'womensTrousersSize', 'womensJeansSize', 'womensFootwearSize'
                ];

                // Get the appropriate charts based on gender
                const charts = sizingChartsData || [];

                sizeFields.forEach(field => {
                  const currentSizeValue = currentData?.data?.stylePreferences?.[field];
                  if (currentSizeValue) {
                    // Find the chart for this field
                    const chart = charts.find((c: any) => c.type === field);
                    if (chart) {
                      // Find the old region
                      const oldRegion = chart.regions?.find((r: any) =>
                        r.name.toLowerCase() === oldSizingStandard.toLowerCase()
                      );

                      // Find the new region
                      const newRegion = chart.regions?.find((r: any) =>
                        r.name.toLowerCase() === sizingStandardValue.toLowerCase()
                      );

                      if (oldRegion && newRegion) {
                        // Find the index of the current size in the old region
                        let currentSizeIndex = -1;
                        let displayValue = currentSizeValue;

                        // Handle different value types from backend
                        if (typeof currentSizeValue === 'object' && currentSizeValue !== null) {
                          displayValue = currentSizeValue.size || currentSizeValue.value || currentSizeValue;
                        }

                        currentSizeIndex = oldRegion.sizes.findIndex((size: any) =>
                          size.value === displayValue || size.label === displayValue
                        );

                        // If we found the size in the old region, map it to the same index in the new region
                        if (currentSizeIndex >= 0 && currentSizeIndex < newRegion.sizes.length) {
                          const newSize = newRegion.sizes[currentSizeIndex];
                          const newSizeValue = newSize.value;

                          // Get the appropriate mutation
                          let mutation: any = null;

                          if (userGender === 'men') {
                            const menMutations = sizeMutations.men;
                            const fieldWithoutSize = field.replace('Size', '');
                            mutation = menMutations[fieldWithoutSize as keyof typeof menMutations];
                          } else if (userGender === 'women') {
                            const womenMutations = sizeMutations.women;
                            const fieldWithoutSize = field.replace('Size', '');
                            mutation = womenMutations[fieldWithoutSize as keyof typeof womenMutations];
                          }

                          // Update the size preference
                          if (mutation?.mutate) {
                            mutation.mutate(
                              {
                                size: newSizeValue,
                                region: sizingStandardValue
                              },
                              {
                                onSuccess: () => {
                                  // Invalidate queries to refresh the UI
                                  queryClient.invalidateQueries({ queryKey: ['stylePreferences'] });
                                },
                                onError: (error: any) => {
                                  console.error(`Failed to update ${field} size:`, error);
                                },
                              }
                            );
                          }
                        }
                      }
                    }
                  }
                });
              }

              // Invalidate the stylePreferences query to refetch the updated data
              queryClient.invalidateQueries({ queryKey: ['stylePreferences'] });
            },
            onError: (error) => {
              console.error('setSizingStandard error:', error);
            }
          });
        }
      },
      queryKey: 'stylePreferences',
      data: stylePreferencesData,
    }];
  }, [sizingChartsData, setSizingStandard, queryClient, stylePreferencesData]);

  // Extract US sizing data and create dynamic options
  const dynamicSizingOptions = useMemo(() => {
    const charts = sizingChartsData || [];
    if (!charts) {
      return {
        generalApparel: [],
        bottoms: [],
        footwear: [],
      };
    }

    // Create sizing options based on gender
    const mutations = sizeMutations[userGender as keyof typeof sizeMutations];

    // Define the mapping of chart types to categories and mutations
    const chartMapping = {
      mensTopsSize: { category: 'generalApparel', title: 'Tops', mutationKey: 'mensTops' },
      mensOuterwearSize: { category: 'generalApparel', title: 'Outerwear', mutationKey: 'mensOuterwear' },
      mensTrousersSize: { category: 'bottoms', title: 'Trousers', mutationKey: 'mensTrousers' },
      mensJeansSize: { category: 'bottoms', title: 'Jeans', mutationKey: 'mensJeans' },
      mensShoesSize: { category: 'footwear', title: 'Shoes', mutationKey: 'mensShoes' },
      womensTopsSize: { category: 'generalApparel', title: 'Tops', mutationKey: 'womensTops' },
      womensDressesSize: { category: 'generalApparel', title: 'Dresses', mutationKey: 'womensDresses' },
      womensTrousersSize: { category: 'bottoms', title: 'Trousers', mutationKey: 'womensTrousers' },
      womensJeansSize: { category: 'bottoms', title: 'Jeans', mutationKey: 'womensJeans' },
      womensFootwearSize: { category: 'footwear', title: 'Footwear', mutationKey: 'womensFootwear' },
    };

    // Filter and organize charts by category
    const config = charts.reduce((acc: Record<string, Array<{ chartKey: string; title: string; dataKey: string; mutationKey: string }>>, chart: any) => {
      const chartType = chart.type;
      const mapping = chartMapping[chartType as keyof typeof chartMapping];

      if (mapping && mutations[mapping.mutationKey as keyof typeof mutations]) {
        const category = mapping.category;
        // Use the chart type as the data key
        const dataKey = chartType;

        if (!acc[category]) {
          acc[category] = [];
        }

        acc[category].push({
          chartKey: chartType,
          title: mapping.title,
          dataKey,
          mutationKey: mapping.mutationKey,
        });
      }

      return acc;
    }, {});

    // Helper function to create sizing options for a category
    const createSizingOptions = (category: keyof typeof config) => {
      const items = config[category] || [];
      return items.map((item: { chartKey: string; title: string; dataKey: string; mutationKey: string }) => {
        // Find the chart data for this item
        const chart = charts.find((c: any) => c.type === item.chartKey);

        // Create a function that gets the current region based on sizing standard
        const getCurrentRegion = () => {
          return chart?.regions?.find((r: any) =>
            r.name.toLowerCase() === (sizingStandard || 'US').toLowerCase()
          );
        };

        // Get sizes from the matching region, or empty array if no match
        const sizes = getCurrentRegion()?.sizes || [];

        // Handle different value types from backend
        const currentValue = stylePreferencesData[item.dataKey];
        let selectedProp = undefined;
        try {
          // Handle different value types from backend
          let displayValue = currentValue;
          if (typeof currentValue === 'object' && currentValue !== null) {
            // If it's an object, try to extract the size value
            displayValue = currentValue.size || currentValue.value || currentValue;
          }

          selectedProp = displayValue ? {
            label: String(displayValue),
            value: String(displayValue)
          } : undefined;
        } catch (error) {
          selectedProp = undefined;
        }

        return {
          title: item.title,
          itemKey: item.dataKey,
          onPress: () => {
            // Handle size selection
          },
          mutation: {
            mutate: (data: any) => {
              // Extract the value from the object passed by SettingsItem
              const sizeValue = data[item.dataKey];

              // Get the mutation based on gender and chart type
              let mutation: any = null;
              if (userGender === 'men') {
                if (item.chartKey === 'mensTopsSize') mutation = sizeMutations.men.mensTops;
                else if (item.chartKey === 'mensOuterwearSize') mutation = sizeMutations.men.mensOuterwear;
                else if (item.chartKey === 'mensTrousersSize') mutation = sizeMutations.men.mensTrousers;
                else if (item.chartKey === 'mensJeansSize') mutation = sizeMutations.men.mensJeans;
                else if (item.chartKey === 'mensShoesSize') mutation = sizeMutations.men.mensShoes;
              } else if (userGender === 'women') {
                if (item.chartKey === 'womensTopsSize') mutation = sizeMutations.women.womensTops;
                else if (item.chartKey === 'womensDressesSize') mutation = sizeMutations.women.womensDresses;
                else if (item.chartKey === 'womensTrousersSize') mutation = sizeMutations.women.womensTrousers;
                else if (item.chartKey === 'womensJeansSize') mutation = sizeMutations.women.womensJeans;
                else if (item.chartKey === 'womensFootwearSize') mutation = sizeMutations.women.womensFootwear;
              }

              if (mutation?.mutate) {
                // Optimistically update the UI
                queryClient.setQueryData(['stylePreferences'], (oldData: any) => {
                  if (oldData?.data?.stylePreferences) {
                    return {
                      ...oldData,
                      data: {
                        ...oldData.data,
                        stylePreferences: {
                          ...oldData.data.stylePreferences,
                          [item.dataKey]: sizeValue
                        }
                      }
                    };
                  }
                  return oldData;
                });

                mutation.mutate(
                  {
                    size: sizeValue,
                    region: sizingStandard || 'US'
                  },
                  {
                    onSuccess: () => {
                      // Invalidate multiple queries to ensure UI updates
                      queryClient.invalidateQueries({ queryKey: ['stylePreferences'] });
                      // Force a refetch after a small delay to ensure backend has processed the update
                      setTimeout(() => {
                        queryClient.refetchQueries({ queryKey: ['stylePreferences'] });
                      }, 100);
                    },
                    onError: (error: any) => {
                      // Revert optimistic update on error
                      queryClient.invalidateQueries({ queryKey: ['stylePreferences'] });
                    },
                  }
                );
              }
            }
          },
          queryKey: 'stylePreferences',
          data: stylePreferencesData,
          selected: selectedProp,
          options: sizes.map((size: any) => ({
            label: size.value,
            value: size.value,
          })),
        };
      });
    };

    return {
      generalApparel: createSizingOptions('generalApparel'),
      bottoms: createSizingOptions('bottoms'),
      footwear: createSizingOptions('footwear'),
    };
  }, [sizingChartsData, userGender, sizeMutations, stylePreferencesData, queryClient]);

  return (
    <>
      <HeaderPage title="Sizing Preferences" />
      <View style={{ gap: 24, marginTop: 24 }}>
        <SettingsSection title="Setting">
          <SettingsGroup
            buttons={dynamicSizingSetting}
          />
        </SettingsSection>
        <SettingsSection title="General Apparel">
          <SettingsGroup
            buttons={dynamicSizingOptions.generalApparel}
          />
        </SettingsSection>
        <SettingsSection title="Bottoms">
          <SettingsGroup
            buttons={dynamicSizingOptions.bottoms}
          />
        </SettingsSection>
        <SettingsSection title="Footwear">
          <SettingsGroup
            buttons={dynamicSizingOptions.footwear}
          />
        </SettingsSection>
      </View>
    </>
  );
}
