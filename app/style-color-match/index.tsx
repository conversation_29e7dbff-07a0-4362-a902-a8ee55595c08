import { Accordion } from '@/components/Accordion';
import { ColorMatch } from '@/components/ColorMatch';
import HeaderPage from '@/components/common/HeaderPage';
import SectionTitle from '@/components/common/SectionTitle';
import TextComponent from '@/components/common/Text';
import { getColorGroups, getStylePreferences } from '@/methods/preferences';
import { View, ActivityIndicator } from 'react-native';
import { useMemo } from 'react';

export default function StyleColorMatch() {
  const { data: colorGroupsData, isLoading: isLoadingColorGroups } = getColorGroups();
  const { data: stylePreferencesData, isLoading: isLoadingPreferences } = getStylePreferences();

  const isLoading = isLoadingColorGroups || isLoadingPreferences;

  // Merge color groups with preferences from stylePreferences
  const colorGroupsWithPreferences = useMemo(() => {
    const colorGroups = colorGroupsData?.data?.colorGroups || [];
    const colorGroupsDataFromPrefs = stylePreferencesData?.data?.stylePreferences?.colorGroups;
    
    // Handle both array and object formats
    let colorGroupPreferences: any = {};
    if (Array.isArray(colorGroupsDataFromPrefs)) {
      // Convert array to object
      colorGroupPreferences = colorGroupsDataFromPrefs.reduce((acc: any, item: any) => {
        if (item._id) {
          acc[item._id] = item.preference || item.value;
        }
        return acc;
      }, {});
    } else if (colorGroupsDataFromPrefs && typeof colorGroupsDataFromPrefs === 'object') {
      colorGroupPreferences = colorGroupsDataFromPrefs;
    }
    
    return colorGroups.map((group: any) => ({
      ...group,
      preference: colorGroupPreferences[group._id] || undefined,
    }));
  }, [colorGroupsData, stylePreferencesData]);

  if (isLoading) {
    return (
      <>
        <HeaderPage noLogo />
        <View style={{ marginTop: 16 }}>
          <SectionTitle title="Style Color Match" />
        </View>
        <View style={{ flex: 1, justifyContent: 'center', alignItems: 'center', marginTop: 24 }}>
          <ActivityIndicator size="large" />
        </View>
      </>
    );
  }

  return (
    <>
      <HeaderPage noLogo />
      <View style={{ marginTop: 16 }}>
        <SectionTitle title="Style Color Match" />
      </View>
      <View style={{ gap: 24, marginTop: 24, marginBottom: 32 }}>
        <TextComponent string="Whether you love neutrals, pastels, bold hues, or monochrome looks, share your favorites so we can match your style effortlessly." />
        {colorGroupsWithPreferences.map((item: any) => (
          <Accordion title={item.groupName} key={item._id}>
            <ColorMatch 
              colors={item.shades || []} 
              _id={item._id}
              initialPreference={item.preference}
            />
          </Accordion>
        ))}
      </View>
    </>
  );
}
