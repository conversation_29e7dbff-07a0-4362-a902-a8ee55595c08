import CalendarList from '@/components/CalendarList';
import CalendarSelection from '@/components/CalendarSelection';
import HeaderDashboard from '@/components/common/HeaderDashboard';
import TodayComponent from '@/components/common/TodayComponent';
import SetYourNextTrip from '@/components/SetYourNextTrip';
import UpcomingTrips from '@/components/UpcomingTrips';
import { getUpcomingTrips } from '@/methods/trips';
import { getUserProfile } from '@/methods/users';
import { ScrollView, View } from 'react-native';

export default function HomeScreen() {
  const { data: userProfile } = getUserProfile();
  const { data: upcomingTrips } = getUpcomingTrips();

  const profile = userProfile?.data.profile;
  const firstName = profile?.name ? profile.name.split(' ')[0] : '';
  const displayName = firstName || profile?.userName || profile?.email;

  // Check if there are upcoming trips
  const hasUpcomingTrips = upcomingTrips?.data?.events?.length > 0;

  return (
    <View>
      <HeaderDashboard title={`Welcome, ${displayName}!`} />
      <ScrollView
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        contentContainerStyle={{ paddingBottom: 220 }}
      >
        {/* <TagLocation /> */}
        <TodayComponent />
        {!hasUpcomingTrips ?
          <View>
            <CalendarSelection />
            <View style={{ marginTop: 16 }}>
              <SetYourNextTrip />
            </View>
          </View> : <View>
            <CalendarSelection />
            <CalendarList />
            <UpcomingTrips />
          </View>}
      </ScrollView>
    </View>
  );
}
