{"expo": {"name": "myuse", "slug": "myuse", "version": "1.1.08", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myuse", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "usesAppleSignIn": true, "bundleIdentifier": "user.myuse.app", "googleServicesFile": "./google-service/GoogleService-Info.plist", "appleTeamId": "9445NHCXCU", "associatedDomains": ["applinks:user.myuse.app"]}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}, "googleServicesFile": "./google-service/google-services.json", "package": "user.myuse.app", "intentFilters": [{"action": "VIEW", "autoVerify": true, "data": [{"scheme": "https", "host": "user.myuse.app"}], "category": ["BROWSABLE", "DEFAULT"]}]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-apple-authentication", "expo-router", "expo-local-authentication", ["expo-font", {"fonts": ["./assets/fonts/Mukta-<PERSON>aani-Font/Mukta<PERSON>aani-Regular.ttf"]}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends.", "cameraPermission": "The app accesses your camera to let you take photos of your clothing items."}], ["expo-location", {"locationAlwaysAndWhenInUsePermission": "The app accesses your location to let you share your location with your friends."}], "expo-secure-store", "@react-native-google-signin/google-signin", "expo-web-browser"], "experiments": {"typedRoutes": true}, "productionUrl": "wss://dev.myuse.world/websocket", "localUrl": "wss://dev.myuse.world/websocket"}}