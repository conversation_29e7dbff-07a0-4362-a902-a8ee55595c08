import {
  ActivityIndicator,
  KeyboardAvoidingView,
  LayoutChangeEvent,
  TouchableOpacity,
  View,
  Image,
  Alert,
  ScrollView,
} from 'react-native';
import {
  AddClothsModalContainer,
  AddClothsModalHeader,
  HeaderText,
  CategoryItem,
  CategoryItemText,
  HeaderTitle,
  ClothsImageContainer,
} from './styles';
import Modal from 'react-native-modal';
import { PlusIcon } from 'lucide-react-native';
import Input from '../common/Input';
import { useState, useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { TitleSection } from '../common/Text';
import { addItem, ItemsUpdate } from '@/methods/cloths';
import * as ImagePicker from 'expo-image-picker';
import { UploadImage, uploadImageToS3 } from '@/methods/cloths';
// Import API-based categories
import { fetchCategoriesFromBackend } from '@/data/gender-categories';
import { getCurrentUserGender } from '@/data/categories';
import { getUserProfile } from '@/methods/users';
import { useRouter } from 'expo-router';
import useResponsive from '@/hooks/useResponsive';
import { ensureMediaLibraryPermission } from '@/utils/permissions';

interface PreSelectedImage {
  uri: string;
  base64?: string;
}

interface AddClothesFromGalleryModalProps {
  isVisible: boolean;
  onClose: () => void;
  preSelectedImage?: PreSelectedImage;
  onSuccess?: (itemId: string) => void;
}

export default function AddClothesFromGalleryModal({
  isVisible,
  onClose,
  preSelectedImage,
  onSuccess,
}: AddClothesFromGalleryModalProps) {
  // Initialize queryClient for cache invalidation
  const queryClient = useQueryClient();
  const router = useRouter();

  // Get responsive values
  const { isTablet, fontSizes, padding, borderRadius } = useResponsive();

  // Calculate responsive image size (similar to Clothing Stats)
  const imageSize = isTablet ? 280 : 200; // Slightly smaller than ClothingStats but much bigger than current 120px

  // Calculate responsive spacing
  const sectionSpacing = isTablet ? 24 : 20;
  const inputPadding = isTablet ? 16 : 12;

  const [width, setWidth] = useState(0);
  const [selectedCategory, setSelectedCategory] = useState<any>('');

  const {
    mutate: addItemMutation,
    isPending: isAddingItem,
    isSuccess: isItemAdded,
    data: itemData,
  } = addItem();

  const {
    mutate: uploadImageMutation,
    isPending: isUploadingImage,
    isSuccess: isImageUploaded,
    data: imageData,
  } = UploadImage();

  const {
    mutate: uploadImageToS3Mutation,
    isPending: isUploadingImageToS3,
    isSuccess: isImageUploadedToS3,
    data: imageDataToS3,
  } = uploadImageToS3();

  const {
    mutate: updateItemMutation,
    isPending: isUpdatingItem,
    isSuccess: isItemUpdated,
    data: itemUpdatedData,
  } = ItemsUpdate();

  // State for gender-specific categories
  const [displayCategories, setDisplayCategories] = useState<any[]>([]);

  // Get user profile to determine gender
  const { data: userProfile } = getUserProfile();

  const [itemName, setItemName] = useState('');
  const [color, setColor] = useState('');
  const [size, setSize] = useState('');
  const [material, setMaterial] = useState('');
  const [image, setImage] = useState('');
  const [displayImage, setDisplayImage] = useState('');
  const [note, setNote] = useState('');
  const [brand, setBrand] = useState('');

  // Initialize with pre-selected image if provided
  useEffect(() => {
    if (preSelectedImage && isVisible) {
      setDisplayImage(preSelectedImage.uri);
      setImage(preSelectedImage.base64 || '');
      console.log('Pre-selected image set:', preSelectedImage.uri);
    }
  }, [preSelectedImage, isVisible]);

  const pickImage = async () => {
    const hasPermission = await ensureMediaLibraryPermission();
    if (!hasPermission) return;
    let result = await ImagePicker.launchImageLibraryAsync({
      mediaTypes: ['images'],
      allowsEditing: false,
      base64: true,
      aspect: [4, 3],
      quality: 1,
    });

    if (!result.canceled) {
      setImage(result.assets[0].base64 || '');
      setDisplayImage(result.assets[0].uri);
    }
  };

  useEffect(() => {
    if (imageDataToS3 && itemData?.itemId) {
      updateItemMutation(
        {
          itemId: itemData?.itemId,
          imageUrl: imageData?.fileURL,
        },
        {
          onSuccess: (response) => {
            console.log('Image updated successfully:', response);
          },
        },
      );
    }
  }, [isImageUploadedToS3]);

  useEffect(() => {
    if (isItemUpdated) {
      // Make sure we have a valid itemId
      if (!itemData?.itemId) {
        Alert.alert(
          'Error',
          'Failed to update item properly. Please try again.',
        );
        return;
      }

      const item = {
        _id: itemData?.itemId,
        name: itemName,
        category: selectedCategory,
        color: color,
        brand: brand,
        imageUrl: imageData?.fileURL,
      };

      // Invalidate relevant queries
      queryClient.invalidateQueries({ queryKey: ['clothes'] });
      queryClient.invalidateQueries({ queryKey: ['item'] });
      queryClient.invalidateQueries({ queryKey: ['categories'] });

      // Force a refresh of the clothes data
      setTimeout(() => {
        queryClient.refetchQueries({ queryKey: ['clothes'] });
      }, 500);

      // Close the modal
      onClose();

      // Navigate to ClothingStats or call success callback
      if (onSuccess) {
        onSuccess(itemData.itemId);
      } else {
        router.push(`/(tabs)/clothing-stats/${itemData.itemId}`);
      }

      // Show success message
      Alert.alert('Success', 'Item added to your closet successfully!');
    }
  }, [isItemUpdated]);

  useEffect(() => {
    if (isImageUploaded) {
      const preSignedUrl = imageData?.preSignedURL;
      const fileType = imageData?.fileType;

      uploadImageToS3Mutation({
        imageUrl: image,
        preSignedUrl: preSignedUrl,
      });
    }
  }, [isImageUploaded]);

  const handleLayout = (event: LayoutChangeEvent) => {
    const { width } = event.nativeEvent.layout;
    setWidth(width);
  };

  useEffect(() => {
    if (isItemAdded) {
      if (image) {
        uploadImageMutation({
          fileName: itemName,
          fileType: 'image/jpeg',
          folderPath: 'items',
          imageUrl: image,
        });
      } else {
        // Make sure we have a valid itemId
        if (!itemData?.itemId) {
          Alert.alert(
            'Error',
            'Failed to add item properly. Please try again.',
          );
          return;
        }

        const item = {
          _id: itemData?.itemId,
          name: itemName,
          category: selectedCategory,
          color: color,
          brand: brand,
          imageUrl: '',
        };

        // Invalidate relevant queries
        queryClient.invalidateQueries({ queryKey: ['clothes'] });
        queryClient.invalidateQueries({ queryKey: ['item'] });
        queryClient.invalidateQueries({ queryKey: ['categories'] });

        // Force a refresh of the clothes data
        setTimeout(() => {
          queryClient.refetchQueries({ queryKey: ['clothes'] });
        }, 500);

        // Close the modal
        onClose();

        // Navigate to ClothingStats or call success callback
        if (onSuccess) {
          onSuccess(itemData.itemId);
        } else {
          router.push(`/(tabs)/clothing-stats/${itemData.itemId}`);
        }

        // Show success message
        Alert.alert('Success', 'Item added to your closet successfully!');
      }
    }
  }, [isItemAdded]);

  // Reset form when modal opens/closes
  useEffect(() => {
    if (isVisible) {
      // Reset form fields when modal opens
      setItemName('');
      setSelectedCategory('');
      setColor('');
      setSize('');
      setBrand('');
      setMaterial('');
      setNote('');

      // Don't reset image fields here as they might be pre-selected
      if (!preSelectedImage) {
        setImage('');
        setDisplayImage('');
      }
    }
  }, [isVisible, preSelectedImage]);

  // Load gender-specific categories
  useEffect(() => {
    const loadGenderCategories = async () => {
      try {
        // Get gender from user profile
        let gender = null;
        if (userProfile?.data?.profile?.gender) {
          gender = userProfile.data.profile.gender;
          console.log('AddClothesFromGallery - Using gender from userProfile:', gender);
        } else {
          // Try to get gender from Meteor.user()
          gender = getCurrentUserGender();
          console.log(
            'AddClothesFromGallery - Using gender from getCurrentUserGender:',
            gender,
          );
        }

        if (!gender) {
          console.warn(
            'AddClothesFromGallery - No gender available, using fallback categories',
          );
          // Use fallback categories
          const fallbackCategories = [
            { _id: 'tops', name: 'Tops', category: 'Tops' },
            { _id: 'bottoms', name: 'Bottoms', category: 'Bottoms' },
            { _id: 'dresses', name: 'Dresses', category: 'Dresses' },
            { _id: 'shoes', name: 'Shoes', category: 'Shoes' },
            {
              _id: 'accessories',
              name: 'Accessories',
              category: 'Accessories',
            },
          ];
          setDisplayCategories(fallbackCategories);
          return;
        }

        // Get categories from backend based on gender
        const backendCategories = await fetchCategoriesFromBackend(gender);

        // Create a Map to track categories by name for deduplication
        const categoryMap = new Map<string, any>();

        // Collect categories by name, keeping only the first occurrence
        // Make sure backendCategories is an array before iterating
        if (!Array.isArray(backendCategories)) {
          console.error(
            'AddClothesFromGallery - backendCategories is not an array:',
            backendCategories,
          );
          return; // Exit early if not an array
        }

        backendCategories.forEach((category) => {
          // Skip if category is undefined or null or has no name
          if (!category || !category.name) {
            console.log('AddClothesFromGallery - Skipping category with no name');
            return;
          }

          if (!categoryMap.has(category.name)) {
            categoryMap.set(category.name, category);
          } else {
            console.log(
              `AddClothesFromGallery - Skipping duplicate category: ${category.name}`,
            );
          }
        });

        // Format categories for display - include all top-level categories (after deduplication)
        const formattedCategories = Array.from(categoryMap.values())
          .filter(
            (category) =>
              category !== null && category !== undefined && category.name,
          ) // Filter out null/undefined and categories with no name
          .map((category) => ({
            _id:
              category.id ||
              `category-${category.name.toLowerCase().replace(/\s+/g, '-')}`,
            name: category.name,
            category: category.name,
          }));

        // Make sure we have all the basic categories at minimum
        const basicCategories = [
          'Tops',
          'Bottoms',
          'Dresses',
          'Shoes',
          'Accessories',
        ];

        // Check if each basic category exists in the formatted categories
        basicCategories.forEach((basicCat) => {
          if (!formattedCategories.some((cat) => cat.name === basicCat)) {
            // Add the missing basic category
            formattedCategories.push({
              _id: `basic-${basicCat.toLowerCase()}`,
              name: basicCat,
              category: basicCat,
            });
          }
        });

        console.log(
          `AddClothesFromGallery - Loaded ${
            formattedCategories.length
          } categories for gender: ${gender || 'unknown'}`,
        );
        setDisplayCategories(formattedCategories);
      } catch (error) {
        console.error('Error loading gender-specific categories:', error);
        // Fallback to a comprehensive set of categories if there's an error
        const fallbackCategories = [
          { _id: 'tops', name: 'Tops', category: 'Tops' },
          { _id: 'bottoms', name: 'Bottoms', category: 'Bottoms' },
          { _id: 'dresses', name: 'Dresses', category: 'Dresses' },
          {
            _id: 'matching-sets',
            name: 'Matching Sets',
            category: 'Matching Sets',
          },
          { _id: 'outerwear', name: 'Outerwear', category: 'Outerwear' },
          { _id: 'swimwear', name: 'Swimwear', category: 'Swimwear' },
          { _id: 'activewear', name: 'Activewear', category: 'Activewear' },
          { _id: 'shoes', name: 'Shoes', category: 'Shoes' },
          { _id: 'jewellery', name: 'Jewellery', category: 'Jewellery' },
          { _id: 'bags', name: 'Bags', category: 'Bags' },
          { _id: 'accessories', name: 'Accessories', category: 'Accessories' },
          { _id: 'tech', name: 'Tech', category: 'Tech' },
          { _id: 'others', name: 'Others', category: 'Others' },
        ];
        setDisplayCategories(fallbackCategories);
      }
    };

    loadGenderCategories();
  }, [userProfile]);

  const saveItem = () => {
    // Validate item name
    if (!itemName || itemName.trim() === '') {
      Alert.alert('Please enter an item name');
      return;
    }

    // Validate category
    if (!selectedCategory) {
      Alert.alert(
        'Please select a category',
        'Select a category in your Myuse closet to add this item',
      );
      return;
    }

    if (!selectedCategory._id) {
      Alert.alert('Error', 'Invalid category selected. Please try again.');
      return;
    }

    // Prepare the data for the API call
    const itemData = {
      name: itemName.trim(),
      itemCategoryId: selectedCategory._id,
      color: color || '',
      brand: brand || '',
    };

    try {
      // Clear any existing clothes cache before adding the item
      queryClient.invalidateQueries({ queryKey: ['clothes'] });

      // Add the item
      addItemMutation(itemData);
    } catch (error) {
      Alert.alert('Error', 'Failed to add item. Please try again.');
    }
  };

  return (
    <Modal
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <KeyboardAvoidingView
        behavior="padding"
        style={{ flex: 1, justifyContent: 'flex-end' }}
      >
        <AddClothsModalContainer>
          {isAddingItem ||
          isUploadingImage ||
          isUploadingImageToS3 ||
          isUpdatingItem ? (
            <ActivityIndicator size={400} color="#0E7E61" />
          ) : (
            <View>
              <AddClothsModalHeader>
                <TouchableOpacity style={{ flex: 1 }} onPress={onClose}>
                  <HeaderText>Cancel</HeaderText>
                </TouchableOpacity>
                <View style={{ flex: 3, alignItems: 'center' }}>
                  <HeaderTitle>Add New Clothes</HeaderTitle>
                </View>
                <View style={{ flex: 1, alignItems: 'flex-end' }}>
                  <TouchableOpacity
                    onPress={() => {
                      saveItem();
                    }}
                  >
                    <HeaderText>Save</HeaderText>
                  </TouchableOpacity>
                </View>
              </AddClothsModalHeader>
              <ScrollView
                showsHorizontalScrollIndicator={false}
                showsVerticalScrollIndicator={false}
                bounces={false}
              >
                {/* Image Section - Now First */}
                <View style={{ marginTop: sectionSpacing }}>
                  <TitleSection string={`Photo`} />
                  <View style={{ marginTop: 10, alignItems: 'center' }}>
                    {displayImage ? (
                      <TouchableOpacity onPress={pickImage}>
                        <Image
                          source={{ uri: displayImage }}
                          style={{
                            width: imageSize,
                            height: imageSize,
                            borderRadius: borderRadius.FIELD_GROUP,
                            backgroundColor: '#F0F0F0',
                            borderWidth: 1,
                            borderColor: 'rgba(0, 0, 0, 0.1)',
                          }}
                          resizeMode="cover"
                        />
                      </TouchableOpacity>
                    ) : (
                      <ClothsImageContainer width={imageSize} onPress={pickImage}>
                        <PlusIcon size={isTablet ? 36 : 32} color="#FFFFFF" />
                      </ClothsImageContainer>
                    )}
                  </View>
                </View>

                {/* Item Name Section - Now Second */}
                <View style={{ marginTop: sectionSpacing }}>
                  <TitleSection string={`Item Name / Description`} />
                  <Input
                    placeholder="Enter item name"
                    value={itemName}
                    onChangeText={setItemName}
                    style={{
                      marginTop: 10,
                      borderWidth: 1,
                      borderColor: '#E0E0E0',
                      borderRadius: borderRadius.FIELD_GROUP,
                      paddingHorizontal: padding.FIELD_HORIZONTAL,
                      paddingVertical: inputPadding,
                      fontSize: fontSizes.INPUT,
                    }}
                  />
                </View>

                {/* Category Section */}
                <View style={{ marginTop: sectionSpacing }}>
                  <TitleSection string={`Category`} />
                  <ScrollView
                    horizontal
                    showsHorizontalScrollIndicator={false}
                    style={{ marginTop: 10 }}
                    contentContainerStyle={{ paddingHorizontal: 5 }}
                  >
                    {displayCategories.map((category) => (
                      <CategoryItem
                        key={category._id}
                        isSelected={selectedCategory._id === category._id}
                        onPress={() => setSelectedCategory(category)}
                      >
                        <CategoryItemText
                          isSelected={selectedCategory._id === category._id}
                        >
                          {category.name}
                        </CategoryItemText>
                      </CategoryItem>
                    ))}
                  </ScrollView>
                </View>

                {/* Color Section */}
                <View style={{ marginTop: sectionSpacing }}>
                  <TitleSection string={`Color (Optional)`} />
                  <Input
                    placeholder="Enter color"
                    value={color}
                    onChangeText={setColor}
                    style={{
                      marginTop: 10,
                      borderWidth: 1,
                      borderColor: '#E0E0E0',
                      borderRadius: borderRadius.FIELD_GROUP,
                      paddingHorizontal: padding.FIELD_HORIZONTAL,
                      paddingVertical: inputPadding,
                      fontSize: fontSizes.INPUT,
                    }}
                  />
                </View>

                {/* Brand Section */}
                <View style={{ marginTop: sectionSpacing }}>
                  <TitleSection string={`Brand (Optional)`} />
                  <Input
                    placeholder="Enter brand"
                    value={brand}
                    onChangeText={setBrand}
                    style={{
                      marginTop: 10,
                      borderWidth: 1,
                      borderColor: '#E0E0E0',
                      borderRadius: borderRadius.FIELD_GROUP,
                      paddingHorizontal: padding.FIELD_HORIZONTAL,
                      paddingVertical: inputPadding,
                      fontSize: fontSizes.INPUT,
                    }}
                  />
                </View>

                {/* Size Section */}
                <View style={{ marginTop: sectionSpacing }}>
                  <TitleSection string={`Size (Optional)`} />
                  <Input
                    placeholder="Enter size"
                    value={size}
                    onChangeText={setSize}
                    style={{
                      marginTop: 10,
                      borderWidth: 1,
                      borderColor: '#E0E0E0',
                      borderRadius: borderRadius.FIELD_GROUP,
                      paddingHorizontal: padding.FIELD_HORIZONTAL,
                      paddingVertical: inputPadding,
                      fontSize: fontSizes.INPUT,
                    }}
                  />
                </View>

                <View style={{ height: isTablet ? 60 : 50 }} />
              </ScrollView>
            </View>
          )}
        </AddClothsModalContainer>
      </KeyboardAvoidingView>
    </Modal>
  );
}
