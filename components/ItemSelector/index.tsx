import { PlusIcon } from 'lucide-react-native';
import { Image, ScrollView, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
export interface Item {
  id: string;
  name: string;
  source: string;
  type: string;
  category: string;
}
interface ItemSelectorProps {
  items: Item[];
  onSelectItem: (item: Item) => void;
  selectedCategory: string;
  onCategoryChange: (category: string) => void;
  handleNewItem: () => void;
}

const ItemSelector: React.FC<ItemSelectorProps & { categories: string[] }> = ({
  items,
  onSelectItem,
  selectedCategory,
  onCategoryChange,
  categories,
  handleNewItem
}) => {
  return (
    <View style={styles.itemSelectorContainer}>
      <View style={styles.categorySelector}>
        <ScrollView
          horizontal
          showsHorizontalScrollIndicator={false}
          showsVerticalScrollIndicator={false}
          style={styles.categoryScroll}
        >
          <TouchableOpacity
            style={[
              styles.categoryItem,
              selectedCategory === 'All' && styles.selectedCategory,
            ]}
            onPress={() => onCategoryChange('All')}
          >
            <Text style={[
              styles.categoryText,
              selectedCategory === 'All' && styles.selectedCategoryText,
            ]}>All</Text>
          </TouchableOpacity>
          {categories.map((category) => (
            <TouchableOpacity
              key={category}
              style={[
                styles.categoryItem,
                selectedCategory === category && styles.selectedCategory,
              ]}
              onPress={() => onCategoryChange(category)}
            >
              <Text style={[
                styles.categoryText,
                selectedCategory === category && styles.selectedCategoryText,
              ]}>{category}</Text>
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
      <View
        style={{
          flexDirection: 'row',
          alignItems: 'center',
          justifyContent: 'space-between',
          marginBottom: 10,
        }}
      >
        <Text style={styles.sectionTitle}>Add an Item</Text>
      </View>
      <ScrollView
        horizontal
        showsHorizontalScrollIndicator={false}
        showsVerticalScrollIndicator={false}
        bounces={false}
        style={styles.itemScroll}
      >
        <TouchableOpacity
          style={styles.itemOption}
          onPress={handleNewItem}
        >
          <View style={{ width: 100, height: 100, backgroundColor: '#EBEBEB', alignItems: 'center', justifyContent: 'center' }}>

            <PlusIcon size={24} color="gray" />
          </View>

          <View style={{ alignItems: 'flex-start', width: '100%' }}>
            <Text style={styles.itemLabel}>Add new item</Text>
          </View>
        </TouchableOpacity>
        {items
          .filter(item => selectedCategory === 'All' || item.category === selectedCategory)
          .map((item, index) => (
            <TouchableOpacity
              key={index}
              style={styles.itemOption}
              onPress={() => onSelectItem(item)}
            >
              <Image
                source={{ uri: item.source }}
                style={styles.itemThumbnail}
                resizeMode="contain"
              />
              <View style={{ alignItems: 'flex-start'}}>
                <Text style={styles.itemLabel}>{item.name}</Text>
              </View>
            </TouchableOpacity>
          ))}
      </ScrollView>
    </View>
  );
};

const styles = StyleSheet.create({
  itemSelectorContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 18,
    fontWeight: 'bold',
    color: '#000',
    marginBottom: 10,
    textAlign: 'center',
  },
  itemScroll: {
    flexDirection: 'row',
  },
  itemOption: {
    marginRight: 15,
    alignItems: 'center',
    width: 100,
  },
  itemThumbnail: {
    width: 100,
    height: 100,
    backgroundColor: '#fff',
    borderRadius: 5,
  },
  itemLabel: {
    color: '#000',
    marginTop: 5,
    fontSize: 12,
  },
  categorySelector: {
    marginBottom: 15,
  },
  categoryScroll: {
    flexDirection: 'row',
    paddingHorizontal: 5,
  },
  categoryItem: {
    paddingHorizontal: 15,
    paddingVertical: 8,
    marginRight: 10,
    borderRadius: 20,
    backgroundColor: '#f0f0f0',
    borderWidth: 1,
    borderColor: '#e0e0e0',
  },
  selectedCategory: {
    backgroundColor: '#0E7E61',
  },
  categoryText: {
    fontSize: 14,
    color: '#333',
  },
  selectedCategoryText: {
    color: '#fff',
  },
});


export default ItemSelector;