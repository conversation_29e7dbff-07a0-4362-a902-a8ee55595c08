import React, { FC, useState, useEffect } from 'react';
import { Text } from 'react-native';
import { useTheme } from 'styled-components/native';
import { updateColorGroup } from '@/methods/preferences';
import {
  ButtonCircle,
  ColorMatchContainer,
  ColorView,
  ColorsContainer,
  PreferenceSliderContainer,
  SliderButton,
  SliderLine,
} from './styles';

type ColorMatchProps = {
  colors: string[];
  _id: string;
  initialPreference?: 'yes' | 'no' | 'maybe';
};

export const ColorMatch: FC<ColorMatchProps> = ({
  colors = [],
  _id,
  initialPreference,
}: ColorMatchProps) => {
  // Map preference strings to numeric values: 'no' -> 0, 'maybe' -> 1, 'yes' -> 2
  const preferenceToValue = (pref?: 'yes' | 'no' | 'maybe'): number => {
    if (pref === 'no') return 0;
    if (pref === 'yes') return 2;
    return 1; // default to 'maybe'
  };

  const [value, setValue] = useState(preferenceToValue(initialPreference));
  const updateColorGroupMutation = updateColorGroup();

  useEffect(() => {
    // Always update when initialPreference changes, even if undefined
    setValue(preferenceToValue(initialPreference));
  }, [initialPreference]);

  const handleValueChange = (newValue: number) => {
    setValue(newValue);
    // Map numeric values to preference strings: 0 -> 'no', 1 -> 'maybe', 2 -> 'yes'
    const preference: 'yes' | 'no' | 'maybe' = 
      newValue === 0 ? 'no' : newValue === 2 ? 'yes' : 'maybe';
    
    updateColorGroupMutation.mutate({ _id, preference });
  };

  return (
    <ColorMatchContainer>
      <ColorsContainer>
        {colors.map((color, index) => (
          <ColorView key={index} style={{ backgroundColor: color }} />
        ))}
      </ColorsContainer>
      <PreferenceSlider value={value} onValueChange={handleValueChange} />
    </ColorMatchContainer>
  );
};

type PreferenceSliderProps = {
  value: number;
  onValueChange: (value: number) => void;
};

const PreferenceSlider: FC<PreferenceSliderProps> = ({
  value,
  onValueChange,
}: PreferenceSliderProps) => {
  const theme = useTheme();

  return (
    <PreferenceSliderContainer>
      <SliderLine />
      <SliderButton
        style={{ alignItems: 'flex-start' }}
        onPress={() => onValueChange(0)}
        activeOpacity={1}
      >
        <ButtonCircle
          style={{
            backgroundColor: value === 0 ? theme.brand.green[500] : '#ffffff',
            borderColor: value === 0 ? theme.brand.green[500] : '#333333',
          }}
        />
        <Text
          style={{
            fontFamily: 'MuktaVaani',
            color: value === 0 ? theme.brand.green[500] : undefined,
          }}
        >
          No
        </Text>
      </SliderButton>
      <SliderButton
        style={{ alignItems: 'center' }}
        onPress={() => onValueChange(1)}
        activeOpacity={1}
      >
        <ButtonCircle
          style={{
            backgroundColor: value === 1 ? theme.brand.green[500] : '#ffffff',
            borderColor: value === 1 ? theme.brand.green[500] : '#333333',
          }}
        />
        <Text
          style={{
            fontFamily: 'MuktaVaani',
            color: value === 1 ? theme.brand.green[500] : undefined,
          }}
        >
          Maybe
        </Text>
      </SliderButton>
      <SliderButton
        style={{ alignItems: 'flex-end' }}
        onPress={() => onValueChange(2)}
        activeOpacity={1}
      >
        <ButtonCircle
          style={{
            backgroundColor: value === 2 ? theme.brand.green[500] : '#ffffff',
            borderColor: value === 2 ? theme.brand.green[500] : '#333333',
          }}
        />
        <Text
          style={{
            fontFamily: 'MuktaVaani',
            color: value === 2 ? theme.brand.green[500] : undefined,
          }}
        >
          Yes
        </Text>
      </SliderButton>
    </PreferenceSliderContainer>
  );
};
