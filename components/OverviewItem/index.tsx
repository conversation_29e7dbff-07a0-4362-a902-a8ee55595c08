import { packingList } from '@/constants/strings';
import {
  CheckI<PERSON>,
  ChevronDownIcon,
  ChevronUpIcon,
  CircleIcon,
  PlusIcon,
  XIcon
} from "lucide-react-native";
import { TouchableOpacity, View } from "react-native";
import { useTheme } from 'styled-components';
import {
  AddItemContainer,
  BrandColorText,
  CircleIconContainer,
  ContentItemButton,
  ContentItemButtonText,
  ItemCountText,
  OverviewItemContainer,
  OverviewItemText,
  OverviewItemTextBold,
  QuantityText,
} from "./styles";

interface OverviewItemProps {
  item: any;
  isActive: boolean;
  onPress: () => void;
  onLongPress: () => void;
  setShowSubItems: (id: string, show: boolean) => void;
  removeCategory: (category: any) => void;
  removeItem: (item: any) => void;
  removeSubItem: (parentId: string, subItemId: string) => void;
  isSubItem: boolean;
  parentId?: string;
  addCloths?: () => void;
  changeItemsActive?: (parentId: string, itemId: string) => void;
  changeAllActiveItems?: (parentId: string, isActive: boolean) => void;
  handleMyUseRecommendations?: () => void;
}

export default function OverviewItem({
  item,
  isActive,
  onPress,
  onLongPress,
  setShowSubItems,
  removeCategory,
  removeItem,
  removeSubItem,
  isSubItem,
  parentId,
  addCloths,
  changeItemsActive,
  changeAllActiveItems,
  handleMyUseRecommendations,
}: OverviewItemProps) {
  const theme = useTheme();
  const truncatedName =
    item?.name?.length > 40 ? item?.name?.substring(0, 40) + "..." : item?.name;

  if (item?.addItem) {
    return (
      <OverviewItemContainer onPress={addCloths}>
        <AddItemContainer>
          <PlusIcon size={20} color={theme.brand.gray[600]} />
          <OverviewItemText
            style={{ color: theme.brand.gray[600] }}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {packingList.addNewItem}
          </OverviewItemText>
        </AddItemContainer>
      </OverviewItemContainer>
    );
  }

  return (
    <OverviewItemContainer
      isItem={item?.type !== "category"}
      disabled={isActive}
      onPress={onPress}
      onLongPress={onLongPress}
    >
      <View style={{ flexDirection: "row", gap: 10 }}>
        {item?.isActive ? (
          <CircleIconContainer
            onPress={() => {
              if (isSubItem && changeItemsActive && parentId) {
                changeItemsActive(parentId, item._id);
              } else {
                changeAllActiveItems(item._id, !item?.isActive);
              }
            }}
          >
            <CheckIcon size={15} color={theme.brand.gray[50]} />
          </CircleIconContainer>
        ) : (
          <CircleIcon
            onPress={() => {
              if (isSubItem && changeItemsActive && parentId) {
                changeItemsActive(parentId, item._id);
              } else {
                changeAllActiveItems(item._id, !item?.isActive);
              }
            }}
            size={20}
            color={theme.brand.green[500]}
          />
        )}
        {item?.type === "category" ? (
          <OverviewItemTextBold numberOfLines={1} ellipsizeMode="tail">
            {truncatedName}
          </OverviewItemTextBold>
        ) : (
          <OverviewItemText numberOfLines={1} ellipsizeMode="tail">
            <View style={{ flexDirection: "column" }}>
              <AddItemContainer >
                <View style={{ width: item.recommendations?.name ? '50%' : '100%'}}>
                  <AddItemContainer >
                    <QuantityText>{item?.quantity}x</QuantityText>
                    <OverviewItemText numberOfLines={1} ellipsizeMode="tail">
                      {truncatedName}
                    </OverviewItemText>
                  </AddItemContainer>
                  {!item?.brand && !item?.color && (
                    <AddItemContainer>
                      <BrandColorText>{packingList.noDescription}</BrandColorText>
                    </AddItemContainer>
                  )}
                </View>
                {/* MyUse Recommendations -- need to check the actual item name with recommendations*/}
                {item.recommendations?.name && (
                  <ContentItemButton onPress={handleMyUseRecommendations}>
                    <ContentItemButtonText>{packingList.myuseRecommendationsButton}</ContentItemButtonText>
                  </ContentItemButton>
                )}
              </AddItemContainer>
              <AddItemContainer>
                <>
                  {item?.brand && <BrandColorText>{item.brand}</BrandColorText>}
                  {item?.brand && item?.color && (
                    <BrandColorText>/</BrandColorText>
                  )}
                  {item?.color && <BrandColorText>{item.color}</BrandColorText>}
                </>
              </AddItemContainer>
            </View>
          </OverviewItemText>
        )}
        {/* add the branch name and color */}
      </View>
      {item?.type === "category" ? (
        <AddItemContainer>
          <ItemCountText>
            ({item?.items?.filter((item: any) => item?.isActive).length} /{" "}
            {item?.items?.length})
          </ItemCountText>
          <TouchableOpacity
            onPress={() => {
              if (setShowSubItems && parentId) {
                setShowSubItems(parentId, !item?.showSubItems);
              }
            }}
          >
            {item?.showSubItems ? (
              <ChevronDownIcon size={20} color={theme.brand.gray[900]} />
            ) : (
              <ChevronUpIcon size={20} color={theme.brand.gray[900]} />
            )}
          </TouchableOpacity>

          <TouchableOpacity onPress={() => removeCategory(item)}>
            <XIcon size={20} color={theme.brand.gray[900]} />
          </TouchableOpacity>
        </AddItemContainer>
      ) : (
        <AddItemContainer>
          <TouchableOpacity
            onPress={() => {
              isSubItem ? onPress() : removeItem(item._id);
            }}
          >
            <PlusIcon size={20} color={theme.brand.gray[900]} />
          </TouchableOpacity>
        </AddItemContainer>
      )}
    </OverviewItemContainer>
  );
}
