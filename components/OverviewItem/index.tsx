import { packingList } from '@/constants/strings';
import {
  CheckI<PERSON>,
  ChevronDownIcon,
  ChevronUpIcon,
  CircleIcon,
  PlusIcon,
  XIcon
} from "lucide-react-native";
import { TouchableOpacity, View } from "react-native";
import { useTheme } from 'styled-components';
import {
  AddItemContainer,
  BrandColorText,
  CircleIconContainer,
  ContentItemButton,
  ContentItemButtonText,
  ItemCountText,
  OverviewItemContainer,
  OverviewItemText,
  OverviewItemTextBold,
  QuantityText,
} from "./styles";

interface OverviewItemProps {
  item: any;
  isActive: boolean;
  onPress: () => void;
  onLongPress: () => void;
  setShowSubItems: (id: string, show: boolean) => void;
  removeCategory: (category: any) => void;
  removeItem: (item: any) => void;
  removeSubItem: (parentId: string, subItemId: string) => void;
  isSubItem: boolean;
  parentId?: string;
  addCloths?: () => void;
  changeItemsActive?: (parentId: string, itemId: string) => void;
  changeAllActiveItems?: (parentId: string, isActive: boolean) => void;
  handleMyUseRecommendations?: () => void;
}

export default function OverviewItem({
  item,
  isActive,
  onPress,
  onLongPress,
  setShowSubItems,
  removeCategory,
  removeItem,
  removeSubItem,
  isSubItem,
  parentId,
  addCloths,
  changeItemsActive,
  changeAllActiveItems,
  handleMyUseRecommendations,
}: OverviewItemProps) {
  const theme = useTheme();
  if (item?.addItem) {
    return (
      <OverviewItemContainer onPress={addCloths}>
        <AddItemContainer>
          <PlusIcon size={20} color={theme.brand.gray[600]} />
          <OverviewItemText
            style={{ color: theme.brand.gray[600] }}
            numberOfLines={1}
            ellipsizeMode="tail"
          >
            {packingList.addNewItem}
          </OverviewItemText>
        </AddItemContainer>
      </OverviewItemContainer>
    );
  }

  return (
    <TouchableOpacity
      style={{
        flexDirection: "row",
        justifyContent: "space-between",
        marginBottom: item?.type !== "category" ? 10 : 0,
        alignItems: "center",
        flex: 1,
      }}
      disabled={isActive}
      onPress={onPress}
      onLongPress={onLongPress}
    >
      {/* 1st row */}
      <View style={{ flexDirection: "row" , alignItems: "flex-start"}}>
        {/* Checkbox */}
        <View style={{ marginRight: 10, marginTop: 5 }}>
          {item?.isActive ? (
            <CircleIconContainer
              onPress={() => {
                if (isSubItem && changeItemsActive && parentId) {
                  changeItemsActive(parentId, item._id);
                } else {
                  changeAllActiveItems(item._id, !item?.isActive);
                }
              }}
            >
              <CheckIcon size={15} color={theme.brand.gray[50]} />
            </CircleIconContainer>
          ) : (
            <CircleIcon
              onPress={() => {
                if (isSubItem && changeItemsActive && parentId) {
                  changeItemsActive(parentId, item._id);
                } else {
                  changeAllActiveItems(item._id, !item?.isActive);
                }
              }}
              size={20}
              color={theme.brand.green[500]}
            />
          )}
        </View>
        {/* Item Name */}
        {item?.type === "category" ? (
          <OverviewItemTextBold>
            {item?.name}
          </OverviewItemTextBold>
        ) : (
          <View style={{ width: item?.name.length > 25 ? '50%' : 'auto' }}>
            <OverviewItemText>
              {item?.name}
            </OverviewItemText>

            {!item?.brand && !item?.color && (
              <AddItemContainer>
                <BrandColorText>{packingList.noDescription}</BrandColorText>
              </AddItemContainer>
            )}
          </View>

        )}
      </View>

      {/* 2nd row */}
      {item?.type === "category" ? (
        <AddItemContainer>
          <ItemCountText>
            ({item?.items?.filter((item: any) => item?.isActive).length} /{" "}
            {item?.items?.length})
          </ItemCountText>
          <TouchableOpacity
            onPress={() => {
              if (setShowSubItems && parentId) {
                setShowSubItems(parentId, !item?.showSubItems);
              }
            }}
          >
            {item?.showSubItems ? (
              <ChevronDownIcon size={20} color={theme.brand.gray[900]} />
            ) : (
              <ChevronUpIcon size={20} color={theme.brand.gray[900]} />
            )}
          </TouchableOpacity>

          <TouchableOpacity onPress={() => removeCategory(item)}>
            <XIcon size={20} color={theme.brand.gray[900]} />
          </TouchableOpacity>
        </AddItemContainer>
      ) : (
        <View>
          {item.recommendations?.name && (
            <ContentItemButton onPress={handleMyUseRecommendations}>
              <ContentItemButtonText>{packingList.myuseRecommendationsButton}</ContentItemButtonText>
            </ContentItemButton>
          )}
        </View>
        // <View style={{ backgroundColor: 'green', width: '10%', alignItems: 'center', justifyContent: 'center' }}>
        //     <TouchableOpacity
        //       onPress={() => {
        //         isSubItem ? onPress() : removeItem(item._id);
        //       }}
        //     >
        //       <PlusIcon size={20} color={theme.brand.gray[900]} />
        //     </TouchableOpacity>
        // </View>
      )
      }

    </TouchableOpacity>
  );
}
