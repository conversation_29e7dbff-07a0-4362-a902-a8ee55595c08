import {
  Color,
  LinearGradient,
  Rect,
  vec
} from '@shopify/react-native-skia';
import moment from 'moment';
import 'moment/locale/en-gb'; // Import British English locale which starts weeks on Monday
import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Alert, Dimensions, FlatList, Linking, Pressable, TouchableOpacity } from 'react-native';
import {
  BoxConnector,
  CalendarContent,
  CalendarContentTitle,
  CalendarListContainer,
  ClothsItem,
  ClothsItemContainer,
  DayItem, DayItemContent, DayItemText, DayItemTextSmall,
  EmptyStateContainer,
  EmptyStateText,
  PlanAnOutfitButtonContainer,
  PlanAnOutfitContainer,
  PlanAnOutfitImage,
  PlanAnOutfitText,
  PlanAnOutfitTitle,
} from './styles';

import Cloudy from '@/assets/svg/cloudy.svg';
import PartlyCloudy from '@/assets/svg/day-cloud.svg';
import Rainy from '@/assets/svg/rainy.svg';
import Sunny from '@/assets/svg/sunny.svg';
import LuggageSvg from '@/assets/svg/luggage.svg';
import { PlanAnOutfit as PlanAnOutfitImg } from '@/constants/images';

import { getOutfits } from '@/methods/outfits';
import { getUpcomingTrips } from '@/methods/trips';
import { getCurrentWeekWeather } from '@/methods/users';
import * as Location from 'expo-location';
import { useRouter } from 'expo-router';
import Button from '../common/Button';

// Set moment locale to British English (starts weeks on Monday)
moment.locale('en-gb');

// Update days array to show exactly 28 days (4 weeks) starting from Monday
const days = Array.from({ length: 28 }, (_, index) => {
  return moment().startOf('isoWeek').add(index, 'days'); // Use isoWeek instead of week
});

// Update weather data to match new days array
const weatherData = Array.from({ length: 28 }, () => ({
  temperature: Math.floor(Math.random() * 30) + 10,
  condition: ['sunny', 'cloudy', 'rainy', 'partly cloudy'][Math.floor(Math.random() * 4)]
}));

// Update weather icon mapping to match API response
const WeatherIcon = ({ condition }: { condition: string }) => {
  const weatherMap: { [key: string]: React.ReactNode } = {
    'Clear': <Sunny style={{ width: 24, height: 24 }} />,
    'Clouds': <Cloudy style={{ width: 24, height: 24 }} />,
    'Rain': <Rainy style={{ width: 24, height: 24 }} />,
    'Drizzle': <PartlyCloudy style={{ width: 24, height: 24 }} />,
    'Thunderstorm': <Rainy style={{ width: 24, height: 24 }} />,
    'Snow': <Cloudy style={{ width: 24, height: 24 }} />,
    'Mist': <PartlyCloudy style={{ width: 24, height: 24 }} />,
  };

  return weatherMap[condition] || <PartlyCloudy style={{ width: 24, height: 24 }} />;
};

// Update gradient colors based on weather condition
const getGradientColors = (condition: string) => {
  const gradientMap: { [key: string]: string[] } = {
    'Clear': ['#FFFFFF', '#E9631A'],
    'Clouds': ['#BCD7EA', '#A8A8A8'],
    'Rain': ['#7AC5FB', '#57636A'],
    'Drizzle': ['#FFFFFF', '#E9631A'],
    'Thunderstorm': ['#7AC5FB', '#57636A'],
    'Snow': ['#BCD7EA', '#A8A8A8'],
    'Mist': ['#FFFFFF', '#E9631A'],
  };

  return gradientMap[condition] || ['#FFFFFF', '#E9631A'];
};

interface WeatherData {
  date: string;
  event: {
    startDateFormatted: string;
  };
}

export default function CalendarList() {
  const [selectedDay, setSelectedDay] = useState(moment().format('YYYY-MM-DD'));
  const [currentPage, setCurrentPage] = useState(0);
  const flatListRef = useRef<FlatList>(null);
  const screenWidth = Dimensions.get('window').width;
  const itemWidth = (screenWidth - 40) / 7;
  const { data: trips, refetch, isLoading } = getUpcomingTrips();
  const router = useRouter();
  const tripData = trips?.data?.events[0];
  console.log('tripData', tripData);

  // Fetch all outfits
  const { data: outfitsData, isLoading: isLoadingOutfits } = getOutfits();
  const allOutfits = outfitsData?.items || [];

  const [currentLocation, setCurrentLocation] = useState<Location.LocationObject | null>(null);
  const DEFAULT_LATITUDE = 37.7749; // San Francisco fallback
  const DEFAULT_LONGITUDE = -122.4194;

  //get the current location of the user

  const getCurrentLocation = useCallback(async () => {
    const { status, canAskAgain } = await Location.requestForegroundPermissionsAsync();
    console.log('location status', status);
    if (status !== 'granted') {
      console.warn('Location permission not granted');
      if (!canAskAgain) {
        Alert.alert(
          'Location Permission Required',
          'Enable location in Settings to get local weather. Using a default location until then.',
          [
            { text: 'Cancel', style: 'cancel' },
            { text: 'Open Settings', onPress: () => Linking.openSettings?.() }
          ]
        );
      }
      return;
    }
    const location = await Location.getCurrentPositionAsync();
    console.log('cuurent location', location)
    setCurrentLocation(location);
  }, []);

  useEffect(() => {
    getCurrentLocation();
  }, []);

  // Get the first day of the current displayed week
  const getCurrentWeekStart = useCallback(() => {
    return moment().startOf('isoWeek').add(currentPage * 7, 'days').toDate();
  }, [currentPage]);

  // Function to get the first day of the week for a given page
  const getFirstDayOfWeekForPage = (page: number) => {
    return moment().startOf('isoWeek').add(page * 7, 'days').format('YYYY-MM-DD');
  };

  const getCurrentWeekEnd = useCallback(() => {
    return moment().startOf('isoWeek').add(currentPage * 7, 'days').add(7, 'days').toDate();
  }, [currentPage]);

  // Fetch weather data for the current week
  const { data: currentWeekWeather, refetch: refetchCurrentWeekWeather, isLoading: isLoadingCurrentWeekWeather } = getCurrentWeekWeather(
    currentLocation?.coords?.latitude ?? DEFAULT_LATITUDE,
    currentLocation?.coords?.longitude ?? DEFAULT_LONGITUDE,
    getCurrentWeekEnd()
  );


  // Refetch weather when week changes
  useEffect(() => {
    refetchCurrentWeekWeather();
  }, [currentPage, refetchCurrentWeekWeather]);

  // Refetch weather when location changes
  useEffect(() => {
    refetchCurrentWeekWeather();
  }, [currentLocation, refetchCurrentWeekWeather]);

  useEffect(() => {
    console.log('currentWeekWeather', currentWeekWeather)
  }, [currentWeekWeather])
  // Get weather data for a specific day
  const getWeatherForDay = useCallback((date: string) => {
    const weatherData = currentWeekWeather?.data?.weeklyWeather || [];
    console.log('weatherData', weatherData)
    return weatherData.find((w: WeatherData) => w?.date === date) || { weather: 'Clear' };
  }, [currentWeekWeather]);



  const renderDayItem = ({ item: day, index }: { item: moment.Moment; index: number }) => {
    const isSelected = selectedDay === day.format('YYYY-MM-DD');
    const dayWeather = getWeatherForDay(day.format('YYYY-MM-DD'));
    const gradientColors = getGradientColors(dayWeather.weather);
    const dayFormatted = day.format('YYYY-MM-DD');
    const tripOnThisDay = trips?.data?.events?.find((trip: any) => {
      if (!trip.startDate) return false;
      const tripStartDate = moment(trip.startDate).format('YYYY-MM-DD');
      return tripStartDate === dayFormatted;
    });

    console.log(dayWeather, 'dayWeather ========');

    return (
      <Pressable
        key={day.format('YYYY-MM-DD')}
        onPress={() => {
          setSelectedDay(day.format('YYYY-MM-DD'));
          const weekPage = Math.floor(index / 7);
          if (currentPage !== weekPage) {
            setCurrentPage(weekPage);
            flatListRef.current?.scrollToIndex({
              index: weekPage * 7,
              animated: true
            });
          }
        }}
        style={{ width: itemWidth, height: 200 }}>
        <DayItem
          selected={isSelected}
          currentDay={day.format('YYYY-MM-DD') === moment().format('YYYY-MM-DD')}>
          <Rect x={0} y={0} width={itemWidth} height={100}>
            <LinearGradient
              colors={gradientColors as Color[]}
              start={vec(0, -60)}
              end={vec(itemWidth, 100)}
            />
          </Rect>
        </DayItem>
        <DayItemContent>
          <WeatherIcon condition={dayWeather.weather} />
          <DayItemText>{day.format('DD')}</DayItemText>
          <DayItemTextSmall>{day.format('ddd')}</DayItemTextSmall>
        </DayItemContent>
        {selectedDay === day.format('YYYY-MM-DD') && <BoxConnector />}
        {tripOnThisDay && (
          <TouchableOpacity
            onPress={() => router.push(`/trip-overview/${tripOnThisDay._id}`)}
            style={{
              marginTop: 10,
              alignSelf: 'center',
              marginRight: 10,
            }}>
            <LuggageSvg style={{ width: 35, height: 30 }} />
          </TouchableOpacity>
        )}
      </Pressable>
    );
  };

  const onViewableItemsChanged = useCallback(({ viewableItems }: any) => {
    if (viewableItems.length > 0) {
      const newPage = Math.floor(viewableItems[0].index / 7);
      setCurrentPage(newPage);
      // Only update selected day on scroll if it's not already selected
      const firstDayOfWeek = getFirstDayOfWeekForPage(newPage);
      if (selectedDay !== firstDayOfWeek) {
        setSelectedDay(firstDayOfWeek);
      }
    }
  }, [selectedDay]);

  const viewabilityConfig = {
    itemVisiblePercentThreshold: 50
  };

  console.log('days', days);

  // Filter outfits for the selected date
  const outfitsForSelectedDate = allOutfits.filter((outfit: any) => {
    if (!outfit.plannedDate && !outfit.eventDate) return false;

    const selectedDateMoment = moment(selectedDay);
    const plannedDate = outfit.plannedDate ? moment(outfit.plannedDate) : null;
    const eventDate = outfit.eventDate ? moment(outfit.eventDate) : null;

    // Check if either plannedDate or eventDate matches the selected date
    const matchesPlannedDate = plannedDate && plannedDate.format('YYYY-MM-DD') === selectedDateMoment.format('YYYY-MM-DD');
    const matchesEventDate = eventDate && eventDate.format('YYYY-MM-DD') === selectedDateMoment.format('YYYY-MM-DD');

    return matchesPlannedDate || matchesEventDate;
  });

  return (
    <>
      <CalendarListContainer>
        <FlatList
          ref={flatListRef}
          style={{ position: 'absolute', top: -70, width: '100%' }}
          data={days}
          renderItem={renderDayItem}
          keyExtractor={(item) => item.format('YYYY-MM-DD')}
          horizontal
          pagingEnabled
          showsHorizontalScrollIndicator={false}
          snapToInterval={screenWidth - 40}
          decelerationRate="fast"
          onViewableItemsChanged={onViewableItemsChanged}
          viewabilityConfig={viewabilityConfig}
          contentContainerStyle={{
            paddingHorizontal: 0,
          }}
          getItemLayout={(data, index) => ({
            length: itemWidth,
            offset: itemWidth * index,
            index,
          })}
        />
      </CalendarListContainer>
      {/* <TouchableOpacity
        onPress={() => router.push(`/trip-overview/${tripData?._id}`)}
        style={{
          marginTop: 10,
          alignSelf: 'flex-end',
          marginRight: 10,
          position: 'absolute',
          top: 175,
          right: 10,
        }}>
        <LuggageSvg style={{ width: 35, height: 30 }} />
      </TouchableOpacity> */}
      <CalendarContent>
        {isLoadingOutfits ? (
          <EmptyStateContainer>
            <EmptyStateText>Loading...</EmptyStateText>
          </EmptyStateContainer>
        ) : outfitsForSelectedDate.length === 0 ? (
          <PlanAnOutfitContainer>
            <PlanAnOutfitTitle>Plan an outfit</PlanAnOutfitTitle>
            <PlanAnOutfitImage source={PlanAnOutfitImg} />
            <PlanAnOutfitText>It will be partly sunny and slightly chilly on Saturday. Dress in light layers to keep warm and bring your sunglasses.</PlanAnOutfitText>
            <PlanAnOutfitButtonContainer>
              <Button title="Plan your outfit" />
              <Button isLined title="Style my outfit" />
            </PlanAnOutfitButtonContainer>
          </PlanAnOutfitContainer>
        ) : (
          <>
            <CalendarContentTitle>
              Worn as planned
            </CalendarContentTitle>
            <ClothsItemContainer>
              {outfitsForSelectedDate.map((outfit: any) => (
                <ClothsItem key={outfit._id} containerWidth={screenWidth - 48}>
                  {/* Outfit content will be displayed here */}
                </ClothsItem>
              ))}
            </ClothsItemContainer>
          </>
        )}
      </CalendarContent>
    </>
  );
}
