import { getPastTrips, getUpcomingTrips } from '@/methods/trips';
import { useRouter } from 'expo-router';
import { CalendarDays, ChevronRight } from 'lucide-react-native';
import moment from 'moment';
import { Container, CalendarSelectionContainer, CalendarSelectionText, SeeAllText, SeeAllTouchable } from './styles';
import { useTheme } from 'styled-components';

export default function CalendarSelection() {
  const theme = useTheme();
  const router = useRouter();
  const { data: upcomingTrips } = getUpcomingTrips();
  const { data: pastTrips } = getPastTrips();

  const hasUpcomingTrips = upcomingTrips?.data?.events?.length > 0;
  const hasPastTrips = pastTrips?.data?.events?.length > 0;

  return (
    <Container>
      <CalendarSelectionContainer>
        <CalendarSelectionText>
          {moment().format('dddd, D MMMM')}
        </CalendarSelectionText>
        <CalendarDays color="#000" size={24} onPress={() => router.navigate('/my-calendar')} />
      </CalendarSelectionContainer>
      {hasPastTrips && !hasUpcomingTrips && (
        <SeeAllTouchable
          onPress={() => router.push({ pathname: '/(tabs)/all-trips', params: { tab: 'past' } })}
          activeOpacity={1}
        >
          <SeeAllText>See All</SeeAllText>
          <ChevronRight size={14} color={theme.brand.green[500]} style={{ marginLeft: 6 }} />
        </SeeAllTouchable>
      )}
    </Container>
  );
}
