import styled from 'styled-components/native';

export const Container = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
`;

export const CalendarSelectionContainer = styled.View`
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
`;

export const CalendarSelectionText = styled.Text`
  font-family: CormorantGaramondSemiBold;
  font-size: 24px;
  color: #000;
`;

export const SeeAllTouchable = styled.TouchableOpacity`
  flex-direction: row;
  align-items: center;
`;

export const SeeAllText = styled.Text`
  font-size: 14px;
  color: ${({ theme }: { theme: any }) => theme.brand.green[500]};
  font-family: 'MuktaVaani';
`;
