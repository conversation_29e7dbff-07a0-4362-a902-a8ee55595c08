import { getUpcomingTrips, getPastTrips } from '@/methods/trips';
import { useFocusEffect, useRouter } from 'expo-router';
import { ChevronRight } from 'lucide-react-native';
import { useCallback, useMemo } from 'react';
import { ActivityIndicator, Text, View } from 'react-native';
import { useTheme } from 'styled-components';
import TripCard from '../TripCard';
import {
  SeeAllText,
  SeeAllTouchable,
  UpcomingTripsContainer,
  UpcomingTripsHeader,
  UpcomingTripsTitle,
} from './styles';
import SetYourNextTrip from '../SetYourNextTrip';

export default function UpcomingTrips() {
  const theme = useTheme();
  const router = useRouter();
  const { data: trips, refetch, isLoading } = getUpcomingTrips();
  const { data: pastTrips } = getPastTrips();

  const tripsData = trips?.data?.events;
  const pastTripsData = pastTrips?.data?.events;

  // Calculate days since last trip
  const numDaysSinceLastTrip = useMemo(() => {
    if (!pastTripsData || pastTripsData.length === 0) {
      return 0;
    }

    // Find the most recent past trip by end date
    const mostRecentTrip = pastTripsData.reduce((latest: any, current: any) => {
      if (!latest) return current;
      
      const latestEndDate = new Date(latest.endDate || latest.destinations?.[0]?.endDate);
      const currentEndDate = new Date(current.endDate || current.destinations?.[0]?.endDate);
      
      return currentEndDate > latestEndDate ? current : latest;
    }, null);

    if (!mostRecentTrip) {
      return 0;
    }

    // Get the end date of the most recent trip
    const tripEndDate = new Date(mostRecentTrip.endDate || mostRecentTrip.destinations?.[0]?.endDate);
    const today = new Date();
    
    // Reset time to start of day for accurate day calculation
    tripEndDate.setHours(0, 0, 0, 0);
    today.setHours(0, 0, 0, 0);
    
    const timeDiff = today.getTime() - tripEndDate.getTime();
    const daysDiff = Math.floor(timeDiff / (1000 * 3600 * 24));
    
    return Math.max(0, daysDiff);
  }, [pastTripsData]);

  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  return (
    <UpcomingTripsContainer>
      <UpcomingTripsHeader>
        <UpcomingTripsTitle>Upcoming</UpcomingTripsTitle>
        <SeeAllTouchable
          onPress={() => router.push({ pathname: '/(tabs)/all-trips', params: { tab: 'upcoming' } })}
          activeOpacity={1}
        >
          <SeeAllText>See All</SeeAllText>
          <ChevronRight size={14} color={theme.brand.green[500]} style={{ marginLeft: 6 }} />
        </SeeAllTouchable>
      </UpcomingTripsHeader>
      <View style={{ gap: 16, marginTop: 16 }}>
        {isLoading ? (
          <ActivityIndicator size="large" color={theme.brand.green[500]} />
        ) : tripsData?.length === 0 ? (
          <SetYourNextTrip numDaysSinceLastTrip={numDaysSinceLastTrip} />
        ) : (
          tripsData?.map((trip: any, index: number) => (
            <TripCard
              key={trip._id}
              trip={trip}
              packingListItems={trip.packingList}
              isDeletable={true}
              onDelete={() => {
                refetch();
              }}
            />
          ))
        )}
      </View>
    </UpcomingTripsContainer>
  );
}
