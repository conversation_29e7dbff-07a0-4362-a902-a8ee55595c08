import { FlatList, Image, Text, TouchableOpacity, View } from "react-native";
import Modal from "react-native-modal";
import { HeaderText } from '../AddItems/styles';
import { TemplateModalContainer, TemplateModalHeader } from '../TemplateListModal/styles';
// To be remove after
import Tshirt from '@/assets/images/tshirt.png';
import { packingList } from '@/constants/strings';
import { ContentContainer, ContentImageContainer, ContentItemBrand, ContentItemColor, ContentItemContainer, ContentItemDescription, ContentItemTitle, ContentText, HeaderTextRecommendation, HeaderViewRecommendation } from './styles';

interface RecommendationModalProps {
  isVisible: boolean;
  onClose: () => void;
  item: any;
}

const RecommendationModal: React.FC<RecommendationModalProps> = ({ isVisible, onClose, item }) => {
  const renderHeader = () => {
    return (
      <TemplateModalHeader>
        <TouchableOpacity onPress={onClose} style={{ width: 50 }}>
          <HeaderText>{packingList.back}</HeaderText>
        </TouchableOpacity>
        <HeaderViewRecommendation>
          <HeaderTextRecommendation>{packingList.myuseRecommendations}{"\n"} {item?.recommendations?.name}</HeaderTextRecommendation>
        </HeaderViewRecommendation>
        <View style={{ width: 50 }} />
      </TemplateModalHeader>
    );
  };

  const renderContent = () => {
    const recommendation = item?.recommendations;
    return (
      <ContentContainer>
        <ContentText>{packingList.myuseRecommendationsText}</ContentText>
        <View style={{ width: '100%' }}>
          {/* <FlatList
            data={item.recommendations}
            showsVerticalScrollIndicator={false}
            ListFooterComponent={<View style={{ width: '100%', height: 100 }} />}
            renderItem={({ item }) => ( */}
              <ContentItemContainer>
                {/* <ContentItemTitle>{recommendation?.name}</ContentItemTitle> */}
                <ContentImageContainer style={{ backgroundColor: '', alignItems: 'center', justifyContent: 'center' }}>
                  <View style={{ width: 300, height: 300 }}>
                    {/* To be change after */}
                    <Image source={{ uri: recommendation?.image }} style={{ width: 300, height: 300 }} />
                  </View>
                  {/* <ContentItemDescription>{item.description}</ContentItemDescription> */}
                </ContentImageContainer>
              {/* ?  <ContentItemColor>{item.color}</ContentItemColor> */}
                {/* <ContentItemBrand>{item.brand}</ContentItemBrand> */}
              </ContentItemContainer>
            {/* )}
          /> */}
        </View>
      </ContentContainer>
    );
  };

  return (
    <Modal
      style={{ justifyContent: 'flex-end', margin: 0 }}
      isVisible={isVisible}
      onBackButtonPress={onClose}
      onBackdropPress={onClose}
    >
      <TemplateModalContainer>
        {renderHeader()}
        {renderContent()}
      </TemplateModalContainer>
    </Modal>
  );
};

export default RecommendationModal;