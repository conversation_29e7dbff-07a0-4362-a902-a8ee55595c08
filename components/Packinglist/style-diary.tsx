import { getStylesDiary } from '@/methods/trips';
import { PencilIcon, PenIcon } from 'lucide-react-native';
import moment from 'moment';
import { useEffect, useState } from 'react';
import {
  ActivityIndicator,
  Dimensions,
  FlatList,
  Image,
  ScrollView,
  StyleSheet,
  Text,
  TouchableOpacity,
  View
} from 'react-native';
import { StyledDiaryItem, StyledDiaryText } from './styles';

import Princess from '@/assets/svg/princess.svg';
import { styleDiary } from '@/constants/strings';
import { router } from 'expo-router';
import Button from '../common/Button';
import StyledDiaryModal from './style-diary-modal';
import { StyledDiaryPlanOutfitModal } from './style-diary-plan-outfit-modal';
import { useFocusEffect } from 'expo-router';
import { useCallback } from 'react';

const { width } = Dimensions.get('window');
const CANVAS_WIDTH = width - 40; // Leaving some margin
const CANVAS_HEIGHT = CANVAS_WIDTH * 1.5; // Aspect ratio of 2:3

export const StyledDiary = ({ eventId, trip }: { eventId: string, trip: string }) => {
  const [activeDay, setActiveDay] = useState<string | null>(moment().format('MM-DD-YYYY'));
  const { data: stylesDiary, refetch, isLoading, error, isRefetching } = getStylesDiary(eventId);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [imageLoadingStates, setImageLoadingStates] = useState<{ [key: string]: boolean }>({});
  const [isPlanOutfitModalVisible, setIsPlanOutfitModalVisible] = useState(false);
  const [activitiesEventsList, setActivitiesEventsList] = useState<any[]>([]);
  const [selectedEvent, setSelectedEvent] = useState<any>(null);
  useFocusEffect(
    useCallback(() => {
      refetch();
    }, [refetch])
  );

  const styleDiaryList = stylesDiary?.data?.styleDiaries?.sort((a: any, b: any) => new Date(a?.date)?.getTime() - new Date(b?.date)?.getTime());

  const activeStyleDiary = styleDiaryList?.find((item: any) => item.date === activeDay);

  useEffect(() => {
    if (styleDiaryList?.length > 0) {
      setActiveDay(styleDiaryList[0].date);
    }
  }, [styleDiaryList]);

  const handleModalClose = () => {
    setIsModalVisible(false);
    refetch();
  };

  const handleImageLoadStart = (imageId: string) => {
    setImageLoadingStates(prev => ({ ...prev, [imageId]: true }));
  };

  const handleImageLoadEnd = (imageId: string) => {
    setTimeout(() => {
      setImageLoadingStates(prev => ({ ...prev, [imageId]: false }));
    }, 300);
  };

  const handleImageError = (imageId: string) => {
    setImageLoadingStates(prev => ({ ...prev, [imageId]: false }));
  };

  const DataItems = ({ item }: { item: { date: string } }) => {
    const isActive = activeDay === item.date;

    return (
      <TouchableOpacity onPress={() => setActiveDay(item.date)}>
        <StyledDiaryItem style={{ borderBottomWidth: isActive ? 1 : 0, borderBottomColor: isActive ? '#0E7E61' : 'transparent' }}>
          <StyledDiaryText style={{ color: isActive ? '#0E7E61' : '#000000' }}>
            {moment(item.date, 'MM-DD-YYYY').format('ddd DD MMM YYYY')}
          </StyledDiaryText>
        </StyledDiaryItem>
      </TouchableOpacity>
    );
  }

  // Loading state for initial data fetch
  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#0E7E61" />
        <Text style={styles.loadingText}>{styleDiary.loadingDiary}</Text>
      </View>
    );
  }

  // Error state
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <Text style={styles.errorText}>{styleDiary.loadingFailed}</Text>
        <TouchableOpacity onPress={() => refetch()} style={styles.retryButton}>
          <Text style={styles.retryButtonText}>{styleDiary.retry}</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Empty state
  if (!styleDiaryList || styleDiaryList.length === 0) {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>{styleDiary.noStyleFound}</Text>
        <TouchableOpacity onPress={() => setIsModalVisible(true)} style={styles.addButton}>
          <PenIcon size={20} color="#0E7E61" />
          <Text style={styles.addButtonText}>{styleDiary.addFirstEntry}</Text>
        </TouchableOpacity>
        <StyledDiaryModal
          activeStyleDiary={activeStyleDiary}
          isVisible={isModalVisible}
          onClose={handleModalClose}
          eventId={eventId}
        />
      </View>
    );
  }

  const navigateToStylingCanvas = (selectedEvent?: any) => {
    setIsPlanOutfitModalVisible(false);
    router.push({
      pathname: '/styling-canvas',
      params: {
        eventId: eventId,
        activeStyleDiary: JSON.stringify(activeStyleDiary),
        selectedEventActivity: JSON.stringify(selectedEvent),
        tripName: trip
      }
    });
  }

  const renderActivitiesEvents = (item: any) => {
    return (
      <>
        <View style={styles.activitiesContainer}>
          <Text style={styles.itemName}>{item?.name}</Text>
          <TouchableOpacity style={styles.editIcon}
            onPress={() => {
              setSelectedEvent(item);
              setIsPlanOutfitModalVisible(true);
            }}>
            <PencilIcon size={20} color="black" />
          </TouchableOpacity>
        </View>
        {!item?.image &&
          <TouchableOpacity
            style={styles.planOutfitButton}
            onPress={() => {
              navigateToStylingCanvas(item);
            }}>
            <Text style={styles.planOutfitButtonText}>{styleDiary.planOutfit}</Text>
          </TouchableOpacity>
        }
      </>
    );
  }
  const isEventHasActiveStyleDiary = () => activitiesEventsList?.length > 0 && activitiesEventsList.filter((event) => event.id === activeStyleDiary?._id).length > 0;

  return (
    <View style={{ marginTop: 40 }}>
      {/* Refetch Loading Overlay */}
      {isRefetching && (
        <View style={styles.refetchOverlay}>
          <View style={styles.refetchContainer}>
            <ActivityIndicator size="small" color="#0E7E61" />
            <Text style={styles.refetchText}>{styleDiary.updating}</Text>
          </View>
        </View>
      )}
      <FlatList
        contentContainerStyle={styles.styleDiaryList}
        horizontal
        data={styleDiaryList || []}
        renderItem={({ item }) => <DataItems item={item} />}
        showsHorizontalScrollIndicator={false}
      />
      <View>
        <ScrollView showsVerticalScrollIndicator={false}>
          <View style={styles.styleDiaryContainer}>
            {activeStyleDiary?.imageURL ? (
              <View style={{ width: '100%' }}>
                {/* {renderActivitiesEvents()} */}
                <View style={styles.imageContainer}>
                  <View style={{ flexDirection: 'row', alignItems: 'center', }}>
                    <Image
                      source={{ uri: activeStyleDiary.imageURL }}
                      style={styles.mainImage}
                      onLoadStart={() => handleImageLoadStart(activeStyleDiary.date)}
                      onLoadEnd={() => handleImageLoadEnd(activeStyleDiary.date)}
                      onError={() => handleImageError(activeStyleDiary.date)}
                    />
                    <View style={styles.editIconContainer}>
                      <TouchableOpacity style={styles.editOutfitButton} onPress={() => navigateToStylingCanvas()}>
                        <Text style={styles.editOutfitButtonText}>{styleDiary.editOutfit}</Text>
                      </TouchableOpacity>
                    </View>
                  </View>

                  {/* Image Loading Indicator */}
                  {imageLoadingStates[activeStyleDiary.date] && (
                    <View style={styles.imageLoadingOverlay}>
                      <ActivityIndicator size="large" color="#0E7E61" />
                      <Text style={styles.imageLoadingText}>{styleDiary.imageLoading}</Text>
                    </View>
                  )}
                </View>
              </View>
            ) :
              isEventHasActiveStyleDiary() ? (
                <View style={{ width: '100%' }}>
                  <View style={{ justifyContent: 'space-between', alignItems: 'center', flexDirection: 'row', marginBottom: 10 }}>
                    <Text style={{ fontFamily: 'MuktaVaani-Regular', fontSize: 16, color: '#000000' }}>
                      {styleDiary.activitiesEvent}
                    </Text>
                    <TouchableOpacity style={styles.addActivityEventButton} onPress={() => setIsPlanOutfitModalVisible(true)}>
                      <Text style={styles.addActivityEventButtonText}>{styleDiary.addActivityEvent}</Text>
                    </TouchableOpacity>
                  </View>
                  <FlatList
                    keyExtractor={(item, index) => `${item.id}-${index}`}
                    data={activitiesEventsList}
                    renderItem={({ item }) => renderActivitiesEvents(item)}
                  />
                </View>
              ) : (
                <View style={styles.placeholderContainer}>
                  <Text style={styles.planAnOutfitText}>{styleDiary.planAnOutfit}</Text>
                  <Princess width={90} height={90} />
                  <Text style={styles.planOutfitMsgText}>{styleDiary.planOutfitMsg}</Text>
                  <Button
                    title={styleDiary.planYourOutfit}
                    style={{ marginTop: 10 }}
                    onPress={() => setIsPlanOutfitModalVisible(true)}
                  />
                  <Button
                    title={styleDiary.styleMyOutfit}
                    isLined
                    style={{ marginTop: 10 }}
                    onPress={() => console.log('Suggest An Outfit')}
                  />
                </View>
              )}
          </View>
        </ScrollView>
      </View>

      <StyledDiaryPlanOutfitModal
        isVisible={isPlanOutfitModalVisible}
        onClose={() => {
          setIsPlanOutfitModalVisible(false);
          setSelectedEvent(null);
        }}
        eventId={eventId}
        handlePlanOutfit={navigateToStylingCanvas}
        activeStyleDiary={activeStyleDiary}
        activitiesEventsList={activitiesEventsList}
        setActivitiesEventsList={setActivitiesEventsList}
        selectedEvent={selectedEvent}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 400,
    marginTop: 40,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
    fontFamily: 'MuktaVaani-Regular',
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 400,
    marginTop: 40,
    padding: 20,
  },
  errorText: {
    fontSize: 16,
    color: '#FF3B30',
    textAlign: 'center',
    marginBottom: 20,
    fontFamily: 'MuktaVaani-Regular',
  },
  retryButton: {
    backgroundColor: '#0E7E61',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 8,
  },
  retryButtonText: {
    color: '#fff',
    fontSize: 16,
    fontFamily: 'MuktaVaani-SemiBold',
  },
  emptyContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    minHeight: 400,
    marginTop: 40,
    padding: 20,
  },
  emptyText: {
    fontSize: 16,
    color: '#666',
    textAlign: 'center',
    marginBottom: 20,
    fontFamily: 'MuktaVaani-Regular',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: '#F0F8F5',
    paddingHorizontal: 20,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
    borderColor: '#0E7E61',
  },
  addButtonText: {
    color: '#0E7E61',
    fontSize: 16,
    fontFamily: 'MuktaVaani-SemiBold',
    marginLeft: 8,
  },
  refetchOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.8)',
    justifyContent: 'flex-start',
    alignItems: 'center',
    zIndex: 999,
    paddingTop: 20,
  },
  refetchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'white',
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3,
    elevation: 3,
  },
  refetchText: {
    marginLeft: 8,
    fontSize: 14,
    color: '#333',
    fontFamily: 'MuktaVaani-Regular',
  },
  imageContainer: {
    width: '100%',
    borderRadius: 10,
    position: 'relative',
  },
  mainImage: {
    width: '70%',
    height: 400,
    borderRadius: 10,
  },
  imageLoadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 10,
  },
  imageLoadingText: {
    marginTop: 10,
    fontSize: 14,
    color: '#333',
    fontFamily: 'MuktaVaani-Regular',
  },
  placeholderContainer: {
    width: '100%',
    borderRadius: 15,
    padding: 20,
    backgroundColor: '#F8F9FA',
    borderWidth: 2,
    borderColor: '#E9ECEF',
    justifyContent: 'center',
    alignItems: 'center',
  },
  activitiesContainer: {
    flexDirection: 'row',
    marginVertical: 10,
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 10
  },
  itemName: {
    fontFamily: 'MuktaVaani-Regular', fontSize: 16, color: '#000000'
  }
  ,
  editIcon: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  planOutfitButton: {
    width: '40%',
    paddingVertical: 8,
    paddingHorizontal: 15,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    backgroundColor: '#0E7E61',
  },
  planOutfitButtonText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 14,
    color: '#fff'
  },
  styleDiaryList: {
    gap: 10,
    paddingBottom: 10,
  },
  styleDiaryContainer: {
    width: '100%',
    borderRadius: 10,
    alignItems: 'flex-end',
    justifyContent: 'center',
  },
  editIconContainer: {
    justifyContent: 'center',
    width: '30%',
    alignItems: 'center'
  },
  editOutfitButton: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderColor: '#000',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    height: 50,
  },
  editOutfitButtonText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 14,
    color: '#000'
  },
  addActivityEventButton: {
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderColor: '#0E7E61',
    borderWidth: 1,
    alignItems: 'center',
    justifyContent: 'center',
    borderRadius: 50,
    height: 50,
  },
  addActivityEventButtonText: {
    fontFamily: 'MuktaVaani-Regular',
    fontSize: 14,
    color: '#0E7E61'
  },
  planAnOutfitText: {
    fontFamily: 'MuktaVaani-Light',
    fontSize: 20,
    marginBottom: 10
  },
  planOutfitMsgText: {
    textAlign: 'center',
    fontFamily: 'MuktaVaani-Light',
    fontSize: 16,
    marginVertical: 10
  },
});
